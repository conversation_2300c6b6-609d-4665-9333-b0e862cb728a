#!/usr/bin/env python3
"""
Migration script to add superseding columns to existing metadata table.

This script safely adds the required columns for superseding functionality:
- lifecycle_status
- superseded_by
- cleanup_reason
- marked_for_deletion_at
- deleted_at
- final_result_table (if missing)

Usage:
    python scripts/migrate_metadata_table.py [--dry-run]
"""

import sys
import os
import argparse
import logging
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.core.connection_manager import ConnectionManager


class MetadataTableMigrator:
    """Migrates the metadata table to support superseding functionality."""
    
    def __init__(self, dry_run: bool = False):
        """Initialize the migrator."""
        self.dry_run = dry_run
        self.connection_manager = ConnectionManager()
        self.connection = None
        self.logger = logging.getLogger(__name__)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def connect(self) -> bool:
        """Connect to the database."""
        try:
            self.connection_manager.initialize()
            self.connection = self.connection_manager.get_clickhouse_connection()
            self.logger.info("Connected to ClickHouse database")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the database."""
        if self.connection_manager:
            self.connection_manager.close()
            self.logger.info("Disconnected from database")
    
    def get_table_columns(self) -> List[str]:
        """Get current columns in the metadata table."""
        try:
            query = """
            SELECT name FROM system.columns 
            WHERE database = 'metadata' AND table = 'results_metadata'
            ORDER BY position
            """
            result = self.connection.get_query_dataframe(query)
            return result['name'].tolist() if not result.empty else []
        except Exception as e:
            self.logger.error(f"Failed to get table columns: {e}")
            return []
    
    def check_missing_columns(self) -> Dict[str, str]:
        """Check which required columns are missing."""
        current_columns = self.get_table_columns()
        
        required_columns = {
            'lifecycle_status': "String DEFAULT 'ACTIVE'",
            'superseded_by': "String DEFAULT ''",
            'cleanup_reason': "String DEFAULT ''",
            'marked_for_deletion_at': "Nullable(DateTime)",
            'deleted_at': "Nullable(DateTime)",
            'final_result_table': "String DEFAULT ''",
            'retention_days': "UInt16 DEFAULT 30",
            'error_info': "String DEFAULT '{}'"
        }
        
        missing_columns = {}
        for col_name, col_type in required_columns.items():
            if col_name not in current_columns:
                missing_columns[col_name] = col_type
        
        return missing_columns
    
    def add_missing_columns(self, missing_columns: Dict[str, str]) -> bool:
        """Add missing columns to the table."""
        if not missing_columns:
            self.logger.info("No missing columns found")
            return True
        
        success = True
        for col_name, col_type in missing_columns.items():
            try:
                query = f"""
                ALTER TABLE metadata.results_metadata 
                ADD COLUMN IF NOT EXISTS {col_name} {col_type}
                """
                
                if self.dry_run:
                    self.logger.info(f"DRY RUN: Would add column {col_name} {col_type}")
                else:
                    self.connection.execute_command(query)
                    self.logger.info(f"Added column: {col_name} {col_type}")
                    
            except Exception as e:
                self.logger.error(f"Failed to add column {col_name}: {e}")
                success = False
        
        return success
    
    def update_existing_data(self) -> bool:
        """Update existing records with default lifecycle_status."""
        try:
            # Update records that don't have lifecycle_status set
            query = """
            ALTER TABLE metadata.results_metadata
            UPDATE lifecycle_status = CASE
                WHEN status = 'done' THEN 'ACTIVE'
                WHEN status = 'error' THEN 'EXPIRED'
                ELSE 'ACTIVE'
            END
            WHERE lifecycle_status = '' OR lifecycle_status IS NULL
            """
            
            if self.dry_run:
                self.logger.info("DRY RUN: Would update existing records with lifecycle_status")
            else:
                self.connection.execute_command(query)
                self.logger.info("Updated existing records with lifecycle_status")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update existing data: {e}")
            return False
    
    def verify_migration(self) -> bool:
        """Verify the migration was successful."""
        try:
            # Check that all required columns exist
            missing_columns = self.check_missing_columns()
            if missing_columns:
                self.logger.error(f"Migration incomplete. Missing columns: {list(missing_columns.keys())}")
                return False
            
            # Check that data was updated correctly
            query = """
            SELECT 
                count() as total_records,
                countIf(lifecycle_status != '') as records_with_lifecycle_status
            FROM metadata.results_metadata
            """
            
            result = self.connection.get_query_dataframe(query)
            if not result.empty:
                row = result.iloc[0]
                total = row['total_records']
                with_status = row['records_with_lifecycle_status']
                
                self.logger.info(f"Verification: {with_status}/{total} records have lifecycle_status")
                
                if total > 0 and with_status == total:
                    self.logger.info("✓ Migration verification successful")
                    return True
                else:
                    self.logger.warning("⚠ Some records may not have been updated properly")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to verify migration: {e}")
            return False
    
    def run_migration(self) -> bool:
        """Run the complete migration process."""
        self.logger.info("Starting metadata table migration for superseding functionality")
        
        if self.dry_run:
            self.logger.info("DRY RUN MODE - No changes will be made")
        
        try:
            # Connect to database
            if not self.connect():
                return False
            
            # Check current state
            current_columns = self.get_table_columns()
            self.logger.info(f"Current table has {len(current_columns)} columns")
            
            # Check missing columns
            missing_columns = self.check_missing_columns()
            if missing_columns:
                self.logger.info(f"Missing columns: {list(missing_columns.keys())}")
            else:
                self.logger.info("All required columns already exist")
            
            # Add missing columns
            if not self.add_missing_columns(missing_columns):
                self.logger.error("Failed to add missing columns")
                return False
            
            # Update existing data
            if not self.update_existing_data():
                self.logger.error("Failed to update existing data")
                return False
            
            # Verify migration (skip in dry run)
            if not self.dry_run:
                if not self.verify_migration():
                    self.logger.error("Migration verification failed")
                    return False
            
            self.logger.info("✓ Metadata table migration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return False
        
        finally:
            self.disconnect()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Migrate metadata table for superseding functionality')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be done without making changes')
    
    args = parser.parse_args()
    
    migrator = MetadataTableMigrator(dry_run=args.dry_run)
    success = migrator.run_migration()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()