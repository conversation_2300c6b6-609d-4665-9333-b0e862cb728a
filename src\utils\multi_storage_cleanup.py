"""
Multi-Storage Cleanup Utilities

This module provides utilities for handling cleanup operations across different
ClickHouse storage types (storage vs cluster connections) and table types
(standard vs distributed tables).
"""

import logging
from typing import Dict, Any, Optional, Tuple
from src.core.clickhouse_driver import ClickHouseConnection
from src.core.connection_manager import ConnectionManager

logger = logging.getLogger(__name__)


class MultiStorageCleanupManager:
    """
    Manager for handling cleanup operations across different storage types.
    
    Handles:
    - Storage connection for standard tables
    - Cluster connection for distributed tables
    - Different DROP TABLE syntax based on table type
    """
    
    def __init__(self, dry_run: bool = True):
        """
        Initialize the multi-storage cleanup manager.
        
        Args:
            dry_run: If True, only simulate operations without making changes
        """
        self.dry_run = dry_run
        self.storage_connection = None
        self.cluster_connection = None
        self.connection_manager = None
        
    def connect(self) -> bool:
        """
        Establish both storage and cluster connections.
        
        Returns:
            True if at least storage connection is successful
        """
        try:
            # Initialize connection manager with cluster mode
            self.connection_manager = ConnectionManager()
            self.connection_manager.initialize(cluster_mode=True)
            
            # Get storage connection (required)
            self.storage_connection = self.connection_manager.get_clickhouse_connection()
            if not self.storage_connection:
                logger.error("Failed to establish storage connection")
                return False
            
            # Get cluster connection (optional)
            try:
                self.cluster_connection = self.connection_manager.get_clickhouse_cluster_connection()
                if self.cluster_connection:
                    logger.info("Both storage and cluster connections established")
                else:
                    logger.warning("Cluster connection not available, using storage connection only")
            except Exception as e:
                logger.warning(f"Cluster connection failed: {e}. Continuing with storage connection only")
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to establish connections: {e}")
            return False
    
    def disconnect(self):
        """Close all connections."""
        if self.connection_manager:
            self.connection_manager.close()
            logger.info("All connections closed")
    
    def determine_storage_type(self, table_name: str) -> Tuple[str, ClickHouseConnection]:
        """
        Determine the appropriate storage type and connection for a table.
        
        Args:
            table_name: Full table name (e.g., 'kpi_result.job_123_distributed')
            
        Returns:
            Tuple of (storage_type, connection) where storage_type is 'cluster' or 'storage'
        """
        # Parse database and table name
        if '.' in table_name:
            database, table = table_name.split('.', 1)
        else:
            database = ''
            table = table_name
        
        # Rule 1: Tables in kpi_result database ending with _distributed use cluster connection
        if database == 'kpi_result' and table.endswith('_distributed'):
            if self.cluster_connection:
                logger.debug(f"Using cluster connection for distributed table: {table_name}")
                return 'cluster', self.cluster_connection
            else:
                logger.warning(f"Cluster connection not available for distributed table {table_name}, falling back to storage")
                return 'storage', self.storage_connection
        
        # Rule 2: All other tables use storage connection
        logger.debug(f"Using storage connection for standard table: {table_name}")
        return 'storage', self.storage_connection
    
    def table_exists(self, table_name: str, connection: ClickHouseConnection) -> bool:
        """
        Check if a table exists using the specified connection.
        
        Args:
            table_name: Full table name to check
            connection: ClickHouse connection to use
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            # Parse table name and optional database name
            if "." in table_name:
                database, table = table_name.split(".", 1)
            else:
                table = table_name
                database = None

            query = f"SELECT 1 FROM system.tables WHERE name = '{table}'"
            if database:
                query += f" AND database = '{database}'"

            result = connection.get_query_dataframe(query)
            return not result.empty

        except Exception as e:
            logger.error(f"Failed to check if table {table_name} exists: {e}")
            return False
    
    def drop_table(self, table_name: str) -> Dict[str, Any]:
        """
        Drop a table using the appropriate connection and syntax.
        
        Args:
            table_name: Full table name to drop
            
        Returns:
            Dictionary with operation results
        """
        result = {
            'success': False,
            'table_name': table_name,
            'storage_type': None,
            'table_existed': False,
            'error': None,
            'dry_run': self.dry_run
        }
        
        try:
            # Determine storage type and connection
            storage_type, connection = self.determine_storage_type(table_name)
            result['storage_type'] = storage_type
            
            # Check if table exists
            table_existed = self.table_exists(table_name, connection)
            result['table_existed'] = table_existed
            
            if not table_existed:
                logger.warning(f"Table {table_name} does not exist")
                result['success'] = True  # Not an error if table doesn't exist
                return result
            
            if self.dry_run:
                logger.info(f"DRY RUN: Would drop {storage_type} table kpi_results.{table_name}")
                result['success'] = True
                return result
            
            # Determine DROP syntax based on storage type and table characteristics
            drop_query = self._build_drop_query(table_name, storage_type)
            
            # Execute the drop command
            logger.info(f"Dropping {storage_type} table: {table_name}")
            logger.debug(f"Drop query: {drop_query}")
            
            connection.execute_command(drop_query)
            
            logger.info(f"Successfully dropped {storage_type} table: {table_name}")
            result['success'] = True
            
        except Exception as e:
            error_msg = f"Failed to drop table {table_name}: {e}"
            logger.error(error_msg)
            result['error'] = error_msg
            
        return result
    
    def _build_drop_query(self, table_name: str, storage_type: str) -> str:
        """
        Build the appropriate DROP TABLE query based on storage type and table characteristics.
        
        Args:
            table_name: Full table name
            storage_type: 'cluster' or 'storage'
            
        Returns:
            DROP TABLE query string
        """
        # Parse database and table name
        if '.' in table_name:
            database, table = table_name.split('.', 1)
        else:
            database = ''
            table = table_name
        
        # For cluster storage with distributed tables, use SYNC syntax
        if storage_type == 'cluster' and database == 'kpi_result' and table.endswith('_distributed'):
            # For distributed tables, we need to drop both replicated and distributed tables
            # Use SYNC to ensure immediate deletion
            return f"DROP TABLE IF EXISTS {table_name} SYNC"
        
        # For all other cases, use standard DROP syntax
        return f"DROP TABLE IF EXISTS {table_name}"
    
    def cleanup_table_batch(self, table_names: list) -> Dict[str, Any]:
        """
        Clean up a batch of tables using appropriate connections.
        
        Args:
            table_names: List of table names to drop
            
        Returns:
            Dictionary with batch cleanup results
        """
        results = {
            'total_tables': len(table_names),
            'successful_drops': 0,
            'failed_drops': 0,
            'tables_not_found': 0,
            'dropped_table_names': [],
            'failed_table_names': [],
            'storage_type_breakdown': {'cluster': 0, 'storage': 0},
            'errors': []
        }
        
        for table_name in table_names:
            drop_result = self.drop_table(table_name)
            
            if drop_result['success']:
                if drop_result['table_existed']:
                    results['successful_drops'] += 1
                    results['dropped_table_names'].append(table_name)
                else:
                    results['tables_not_found'] += 1
                
                # Track storage type usage
                if drop_result['storage_type']:
                    results['storage_type_breakdown'][drop_result['storage_type']] += 1
            else:
                results['failed_drops'] += 1
                results['failed_table_names'].append(table_name)
                if drop_result['error']:
                    results['errors'].append(drop_result['error'])
        
        logger.info(f"Batch cleanup completed: {results['successful_drops']} successful, "
                   f"{results['failed_drops']} failed, {results['tables_not_found']} not found")
        
        return results
