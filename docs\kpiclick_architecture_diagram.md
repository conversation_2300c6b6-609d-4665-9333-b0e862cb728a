# KPIClick Project Architecture Diagram

This diagram shows the complete workflow and architecture of the KPIClick project, including main entry points, core modules, data flow, external dependencies, and processing stages.

```mermaid
graph TD
    %% Entry Points and Command Line Interface
    CLI["Command Line Interface<br/>main.py"] --> ARGS["Parse Arguments<br/>job_id, username, output_dir<br/>cluster_mode, cluster_storage_mode"]

    %% Application Initialization
    ARGS --> INIT["Application Initialization<br/>initialize_app()"]
    INIT --> CONFIG["Configuration Loading<br/>config.py - Environment Variables"]
    INIT --> LOGGING["Logging Setup<br/>File & Console Handlers"]
    INIT --> CONN_MGR["Connection Manager<br/>Database Connections"]
    
    %% Configuration and External Services
    CONFIG --> CH_CONFIG["ClickHouse Settings<br/>Host, Port, User, Password"]
    CONFIG --> CH_CLUSTER_CONFIG["ClickHouse Cluster Settings<br/>Distributed Processing"]
    CONFIG --> PG_CONFIG["PostgreSQL Settings<br/>Metadata Storage"]
    CONFIG --> MG_CONFIG["Magic Gateway API<br/>Object Resolution"]
    CONFIG --> JOB_CONFIG["Job API Settings<br/>Status & Progress Updates"]

    %% Connection Management
    CONN_MGR --> CH_CONN["ClickHouse Connection<br/>clickhouse-connect<br/>Data Storage"]
    CONN_MGR --> CH_CLUSTER_CONN["ClickHouse Cluster Connection<br/>clickhouse-driver<br/>Distributed Processing"]

    %% Main Processing Flow
    INIT --> JOB_SERVICE["JobService<br/>Main Processing Orchestrator"]
    JOB_SERVICE --> FETCH_JOB["Fetch Job Request<br/>job_api_client.get_job_request()"]

    %% External API Integration
    FETCH_JOB --> JOB_API["Job Management API<br/>Job Status & Progress<br/>Request/Response Data"]

    %% Job Processing Pipeline
    FETCH_JOB --> CREATE_REQUEST["Create KPIRequest Model<br/>Validate Input Data"]
    CREATE_REQUEST --> JOB_PARAMS["Create JobParameters<br/>Centralized Data Model"]
    JOB_PARAMS --> PROGRESS["Initialize ProgressTracker<br/>Real-time Progress Updates"]
    
    %% Data Validation and Preparation Stage
    PROGRESS --> VALIDATION_STAGE["VALIDATION STAGE<br/>Data Validation & Preparation"]
    VALIDATION_STAGE --> AXIS_SERVICE["AxisService<br/>Object & Filter Processing"]

    %% Axis Service Components
    AXIS_SERVICE --> OBJ_PROCESSOR["ObjectProcessor<br/>Standard Name-based Resolution"]
    AXIS_SERVICE --> JSON_PROCESSOR["JsonObjectProcessor<br/>ID-based Resolution for Cluster"]
    AXIS_SERVICE --> VALIDATION_PROCESSOR["ValidationProcessor<br/>Data Validation & Enrichment"]

    %% External Magic Gateway Integration
    OBJ_PROCESSOR --> MG_API["Magic Gateway API<br/>AsyncMagicGatewayClient<br/>Object Metadata Resolution"]
    JSON_PROCESSOR --> MG_API
    JSON_PROCESSOR --> CLUSTER_API["Cluster API Client<br/>Object Definitions & DDL<br/>ID-based Resolution"]

    %% Query Generation Stage
    VALIDATION_STAGE --> QUERY_STAGE["QUERY GENERATION STAGE<br/>SQL Query Building"]
    QUERY_STAGE --> QUERY_SERVICE["QueryService<br/>Query Management"]
    QUERY_SERVICE --> QUERY_BUILDER["QueryBuilder<br/>ClickHouseQuery<br/>SQL Template Rendering"]
    QUERY_SERVICE --> QUERY_PROCESSOR["QueryProcessor<br/>Query Execution Engine"]

    %% Query Templates and SQL Generation
    QUERY_BUILDER --> TEMPLATES["SQL Templates<br/>Jinja2 Templates<br/>templates/, templates_cluster/"]
    QUERY_BUILDER --> TEMPLATE_MGR["SQLTemplateManager<br/>Template Loading & Rendering"]
    
    %% Query Execution Stage
    QUERY_STAGE --> EXECUTION_STAGE["QUERY EXECUTION STAGE<br/>Period-by-Period Processing"]
    EXECUTION_STAGE --> RESOURCE_CHECK["ResourceCheck<br/>Memory & System Resources"]
    EXECUTION_STAGE --> PERIOD_LOOP["Process Each Period<br/>Sequential Execution"]

    %% Query Processing Modes
    PERIOD_LOOP --> STANDARD_QUERY["Standard Query Processing<br/>Single ClickHouse Instance"]
    PERIOD_LOOP --> CLUSTER_QUERY["Cluster Query Processing<br/>Distributed Tables & Execution"]

    %% Database Execution
    STANDARD_QUERY --> CH_EXEC["ClickHouse Execution<br/>clickhouse-connect<br/>Local Processing"]
    CLUSTER_QUERY --> CH_CLUSTER_EXEC["ClickHouse Cluster Execution<br/>clickhouse-driver<br/>Distributed Processing"]

    %% Data Storage Stage
    EXECUTION_STAGE --> STORAGE_STAGE["DATA STORAGE STAGE<br/>Result Persistence"]
    STORAGE_STAGE --> STORAGE_SERVICE["StorageService<br/>Multi-mode Storage Management"]

    %% Storage Processors
    STORAGE_SERVICE --> LOCAL_STORAGE["LocalStorageProcessor<br/>Local ClickHouse Storage"]
    STORAGE_SERVICE --> CLUSTER_STORAGE["ClusterStorageProcessor<br/>Cluster Storage Mode"]
    STORAGE_SERVICE --> METADATA_STORAGE["MetadataStorageProcessor<br/>Job Metadata Management"]

    %% Storage Destinations
    LOCAL_STORAGE --> CH_RESULTS["ClickHouse Results Tables<br/>Local Storage"]
    CLUSTER_STORAGE --> CH_CLUSTER_RESULTS["ClickHouse Cluster Results<br/>job_result Database"]
    METADATA_STORAGE --> CH_METADATA["ClickHouse Metadata Tables<br/>results_metadata, result_access"]
    
    %% Progress Tracking and Status Updates
    PROGRESS --> PROGRESS_API["Progress API Updates<br/>Real-time Status Communication"]
    PROGRESS_API --> JOB_API

    %% Error Handling and Logging
    JOB_SERVICE --> ERROR_HANDLER["Error Handling<br/>ClickHouse Error Parser<br/>Simplified Error Messages"]
    ERROR_HANDLER --> LOG_FILES["Log Files<br/>memlog/ Directory<br/>User-specific Logs"]

    %% Final Results and Completion
    STORAGE_STAGE --> FINALIZATION["FINALIZATION STAGE<br/>Job Completion"]
    FINALIZATION --> RESULT_MODEL["KPIResult Model<br/>result_ids, errors, status"]
    RESULT_MODEL --> JOB_STATUS["Final Job Status<br/>done or error"]
    JOB_STATUS --> JOB_API

    %% Export and Output
    FINALIZATION --> EXPORTS["Export Files<br/>CSV, XLSX Formats<br/>exports/ Directory"]
    
    %% Styling for different component types
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef coreService fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalAPI fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef processing fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef config fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    %% Apply styles
    class CLI,ARGS entryPoint
    class JOB_SERVICE,AXIS_SERVICE,QUERY_SERVICE,STORAGE_SERVICE coreService
    class CH_CONN,CH_CLUSTER_CONN,CH_EXEC,CH_CLUSTER_EXEC,CH_RESULTS,CH_CLUSTER_RESULTS,CH_METADATA database
    class JOB_API,MG_API,CLUSTER_API,PROGRESS_API externalAPI
    class VALIDATION_STAGE,QUERY_STAGE,EXECUTION_STAGE,STORAGE_STAGE,FINALIZATION processing
    class LOCAL_STORAGE,CLUSTER_STORAGE,METADATA_STORAGE,EXPORTS storage
    class CONFIG,CH_CONFIG,CH_CLUSTER_CONFIG,PG_CONFIG,MG_CONFIG,JOB_CONFIG config
```

## Architecture Overview

### Main Entry Points
- **Command Line Interface (main.py)**: Primary entry point accepting job_id, username, and optional parameters
- **Application Initialization**: Sets up logging, configuration, and database connections

### Core Services Architecture
1. **JobService**: Main orchestrator managing the complete job processing pipeline
2. **AxisService**: Handles object and filter data processing with dual resolution modes
3. **QueryService**: Manages SQL query building and execution
4. **StorageService**: Coordinates multi-mode data storage operations

### Data Flow Stages
1. **Validation Stage**: Data validation, object resolution, and preparation
2. **Query Generation Stage**: SQL template rendering and query building
3. **Query Execution Stage**: Period-by-period query processing with resource management
4. **Data Storage Stage**: Result persistence with multiple storage modes
5. **Finalization Stage**: Job completion, status updates, and export generation

### External Dependencies
- **Magic Gateway API**: Object metadata resolution and validation
- **Job Management API**: Job status, progress updates, and request/response handling
- **Cluster API**: ID-based object resolution for distributed processing
- **ClickHouse**: Primary data storage and query execution engine
- **PostgreSQL**: Metadata and configuration storage

### Processing Modes
- **Standard Mode**: Single ClickHouse instance processing
- **Cluster Mode**: Distributed processing across ClickHouse cluster
- **Cluster Storage Mode**: Results stored in cluster's job_result database

### Key Features
- **Real-time Progress Tracking**: Comprehensive progress updates throughout processing
- **Dual Object Resolution**: Name-based (standard) and ID-based (cluster) object resolution
- **Multi-mode Storage**: Local, cluster, and distributed storage options
- **Comprehensive Error Handling**: ClickHouse error parsing and simplified error messages
- **Template-based SQL Generation**: Jinja2 templates for flexible query building
