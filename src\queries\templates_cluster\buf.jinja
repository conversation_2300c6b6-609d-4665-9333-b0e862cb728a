{# Buyers Uplift Factor calculation for cases with hh axis #}
WITH rp_data AS (
SELECT
    rwbasis,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    sum(a.buyers_ww) AS buyers_ww,
    sum(a.buyers_ww * (trips_raw = 1)) AS trial_ww,
    sum(trips_ww) AS trips_ww,
    sum(trips_fullmass) AS trips_fullmass,
    {% if catman %} sum(value_rp) AS value, {% endif %}
    any(population) AS rw_population
FROM buyers_final_{{ table_suffix }}_distributed a
GROUP BY 
    rwbasis
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                , {{ axis_key }}_position_number
            {% endif %}
        {% endif %}
    {% endfor %}
)
SELECT
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    BUF,
    rwbasis,
    buyers_ww
    {% if catman %}, value {% endif %}
    {% if ("axsh" not in axes.values()|map(attribute="type") or (filters is not none and "flth" not in filters.values()|map(attribute="type"))) and facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
        trial_ww,
        repeaters_ww
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
        p_trial_trial,
        p_rep_rep,
        p_rep_trial,
        p_trial_rep
    {% endif %}
FROM (
    SELECT
        x5.*
        {% if facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
            CASE
                WHEN x5.trial_ww = 0 THEN 0.
                WHEN x5.repeaters_rp >= x5.repeaters_ww THEN LEAST(1, x5.trial_rp / x5.trial_ww / x5.BUF)
                ELSE 1.
            END AS p_trial_trial,
            CASE
                WHEN x5.repeaters_ww = 0 THEN 0.
                WHEN x5.repeaters_rp >= x5.repeaters_ww THEN 1.
                ELSE LEAST(1, x5.repeaters_rp / x5.repeaters_ww / x5.BUF)
            END AS p_rep_rep,
            CASE
                WHEN x5.repeaters_rp >= x5.repeaters_ww THEN 0.
                ELSE 1. - p_rep_rep
            END AS p_trial_rep,
            CASE
                WHEN x5.repeaters_rp >= x5.repeaters_ww THEN 1. - p_trial_trial
                ELSE 0.
            END AS p_rep_trial
        {% endif %}
    FROM (
        SELECT
            x4.*,
            {% if facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %}
                GREATEST(0, x4.buyers_rp - x4.trial_rp) AS repeaters_rp,
            {% endif %}
            CASE 
                WHEN x4.buyers_ww = 0 THEN 0.
                ELSE x4.buyers_rp / x4.buyers_ww
            END AS BUF
        FROM (
            SELECT
                x3.*,
                x3.rw_population * (1 - x3.p0_corr) AS buyers_rp
                {% if facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
                    CASE
                        WHEN (x3.trips_ww = 0 AND x3.k IS NULL) THEN 0.
                        WHEN x3.k IS NULL THEN x3.trial_ww * x3.trips_fullmass / x3.trips_ww
                        WHEN x3.um / x3.m >= 1 THEN x3.trial_ww + x3.rw_population * (x3.um * p_trial(x3.um, x3.k) - x3.m * p_trial(x3.m, x3.k))
                        ELSE x3.trial_ww * x3.um * p_trial(x3.um, x3.k) / x3.m * p_trial(x3.m, x3.k)
                    END AS trial_rp,
                    x3.buyers_ww - x3.trial_ww AS repeaters_ww
                {% endif %}
            FROM (
                SELECT
                    x2.*,
                    CASE
                        WHEN (x2.trips_ww = 0 AND x2.k IS NULL) THEN 1.
                        WHEN x2.p0_obs = 0 THEN 0
                        WHEN x2.k IS NULL THEN GREATEST(0, 1 - x2.trips_fullmass / x2.trips_ww * x2.buyers_ww / x2.rw_population)
                        ELSE p_nonbuyer(x2.um, x2.k)
                    END AS p0_corr
                FROM (
                    SELECT
                        x1.*,
                        CASE
                            WHEN (x1.p0_obs = 0) OR (x1.m < -LN(x1.p0_obs)) THEN NULL
                            ELSE find_k(x1.p0_obs, x1.m)
                        END AS k
                    FROM (
                        SELECT
                            rp_data.*,
                            1 - rp_data.buyers_ww / rp_data.rw_population AS p0_obs,
                            rp_data.trips_ww / rp_data.rw_population AS m,
                            rp_data.trips_fullmass / rp_data.rw_population AS um
                        FROM rp_data
                    ) AS x1
                ) AS x2
            ) AS x3
        ) AS x4
    ) AS x5
)