"""
Connection Manager for the application.

This module provides a centralized manager for database connections.
It handles connection pooling, lifecycle management, and configuration.
"""

import logging
from typing import Union

from src.core.clickhouse_connect import ClickHouseConnection
from src.core.clickhouse_driver import (
    ClickHouseConnection as ClickHouseDriverConnection,
)
from src.core.config import config


class ConnectionManager:
    """
    Manages database connections for the application.

    This class provides a centralized way to manage database connections,
    including connection pooling, lifecycle management, and configuration.
    """

    _instance = None

    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super(ConnectionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the connection manager."""
        if getattr(self, "_initialized", False):
            return

        self.logger = logging.getLogger(self.__class__.__name__)
        self.clickhouse_connection = None
        self.clickhouse_cluster_connection = None
        self._initialized = True

    def initialize(self, cluster_mode: bool = False):
        """Initialize connections.

        Args:
            cluster_mode: Whether to initialize cluster connections
        """
        mode_indicator = "[CLUSTER MODE]" if cluster_mode else "[NORMAL MODE]"
        self.logger.info(f"{mode_indicator} Initializing database connections")
        self._initialize_clickhouse_connection()

        if cluster_mode:
            self.logger.info(
                "[CLUSTER MODE] Initializing cluster connection for distributed processing"
            )
            self._initialize_clickhouse_cluster_connection()
            self.logger.info(
                "[CLUSTER MODE] Dual connections initialized - storage and cluster connections ready"
            )
        else:
            self.logger.info(
                "[NORMAL MODE] Single connection initialized - storage connection ready"
            )

    def reinitialize(self, cluster_mode: bool = False):
        """Reinitialize all connections.

        This method closes existing connections and initializes new ones.

        Args:
            cluster_mode: Whether to initialize cluster connections
        """
        self.logger.info("Reinitializing connection manager")
        self.close()
        self.initialize(cluster_mode)

    def _initialize_clickhouse_connection(self):
        """Initialize the ClickHouse connection."""
        if self.clickhouse_connection is not None:
            # self.logger.info("ClickHouse storage connection already initialized")
            # return
            self.logger.info("Reinitializing ClickHouse storage connection")

        self.logger.info("Initializing ClickHouse storage connection")
        self.clickhouse_connection = ClickHouseConnection(
            host=config.clickhouse_host,
            port=config.clickhouse_port,
            user=config.clickhouse_user,
            password=config.clickhouse_password,
            database=config.clickhouse_database,
            connection_type="storage",
        )

        if not self.clickhouse_connection.client:
            self.logger.error("Failed to initialize ClickHouse storage connection")
            raise ConnectionError("Failed to initialize ClickHouse storage connection")

        self.logger.info("ClickHouse storage connection initialized successfully")

    def _initialize_clickhouse_cluster_connection(self):
        """Initialize the ClickHouse cluster connection using configured client."""
        if self.clickhouse_cluster_connection is not None:
            self.logger.info("ClickHouse cluster connection already initialized")
            return

        client_type = config.clickhouse_cluster_client_type
        port = config.clickhouse_cluster_port

        self.logger.info(f"[CLUSTER MODE] Using {client_type} for cluster connections")
        self.logger.info(f"[CLUSTER MODE] Cluster connection port: {port}")

        if client_type == "clickhouse_connect":
            self.clickhouse_cluster_connection = ClickHouseConnection(
                host=config.clickhouse_cluster_host,
                port=port,
                user=config.clickhouse_cluster_user,
                password=config.clickhouse_cluster_password,
                database=config.clickhouse_cluster_database,
                connection_type="cluster",
            )
        else:  # clickhouse_driver
            self.clickhouse_cluster_connection = ClickHouseDriverConnection(
                host=config.clickhouse_cluster_host,
                port=port,
                user=config.clickhouse_cluster_user,
                password=config.clickhouse_cluster_password,
                database=config.clickhouse_cluster_database,
                settings={
                    "log_level": "warning"
                },  # Reduce logging from clickhouse-driver (i.e. Stack trace)
                connection_type="cluster",
            )

        if not self.clickhouse_cluster_connection.client:
            self.logger.error(
                f"Failed to initialize ClickHouse cluster connection using {client_type}"
            )
            raise ConnectionError(
                f"Failed to initialize ClickHouse cluster connection using {client_type}"
            )

        self.logger.info(
            f"ClickHouse cluster connection initialized successfully using {client_type}"
        )

    def get_clickhouse_connection(self) -> ClickHouseConnection:
        """
        Get the ClickHouse connection.

        Returns:
            ClickHouse connection

        Raises:
            ConnectionError: If the connection is not initialized
        """
        if self.clickhouse_connection is None:
            self.logger.error("ClickHouse connection not initialized")
            raise ConnectionError("ClickHouse connection not initialized")

        return self.clickhouse_connection

    def get_clickhouse_cluster_connection(
        self,
    ) -> Union[ClickHouseConnection, ClickHouseDriverConnection]:
        """
        Get the ClickHouse cluster connection.

        Returns:
            ClickHouse cluster connection (either ClickHouseConnection or ClickHouseDriverConnection)

        Raises:
            ConnectionError: If the cluster connection is not initialized
        """
        if self.clickhouse_cluster_connection is None:
            self.logger.error("ClickHouse cluster connection not initialized")
            raise ConnectionError("ClickHouse cluster connection not initialized")

        return self.clickhouse_cluster_connection

    def close(self):
        """Close all connections."""
        self.logger.info("Closing all database connections")

        if self.clickhouse_connection is not None:
            self.clickhouse_connection.close()
            self.clickhouse_connection = None
            self.logger.info("ClickHouse connection closed")

        if self.clickhouse_cluster_connection is not None:
            self.clickhouse_cluster_connection.close()
            self.clickhouse_cluster_connection = None
            self.logger.info("ClickHouse cluster connection closed")


# Create a singleton instance
connection_manager = ConnectionManager()
