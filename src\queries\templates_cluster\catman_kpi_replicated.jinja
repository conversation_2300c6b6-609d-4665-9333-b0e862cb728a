{# Template for creating replicated table for final KPI data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.final_{{ table_suffix }}_replicated
(
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number String,
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
        buyers_raw Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["penetration", "frequency", "buyers_in_store", "spend_per_buyer", "buyers_potential", "closure_rate"])|list %}
        buyers_in_shop Float64,
        buyers_potential Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["frequency", "packs_per_trip", "trips_in_store", "spend_per_trip", "trips_potential"])|list %}
        trips_in_store Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["trips_potential"])|list %}
       trips_potential Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["buyers_anywhere"])|list %}
        buyers_anywhere_ww Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_in_store", "vpe", "shopper_loyalty"])|list %}
        value_in_shop Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["packs_in_store"])|list %}
        number_rp Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_potential", "propensity", "vpe"])|list %}
        value_potential Float64,
        value_potential_total Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["shopper_loyalty"])|list %}
        loyalty_base Float64,
    {% endif %}
    {% if "BUF" in required_facts or "population" in required_facts %}
        population Float64,
    {% endif %}
    projectc Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    projectc
)
SETTINGS index_granularity = 8192
