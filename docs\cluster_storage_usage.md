# Cluster Storage Service

The Storage Service supports distributed table creation for cluster environments. This feature automatically creates distributed tables alongside local tables when cluster mode is enabled.

## Configuration

To enable cluster mode, you need to set the following environment variables:

```bash
# Cluster connection settings
CLICKHOUSE_CLUSTER_HOST=cluster-host
CLICKHOUSE_CLUSTER_PORT=9000
CLICKHOUSE_CLUSTER_USER=cluster-user
CLICKHOUSE_CLUSTER_PASSWORD=cluster-password
CLICKHOUSE_CLUSTER_DATABASE=cluster-database
CLICKHOUSE_CLUSTER_NAME=cluster-name
```

## Usage

### Command Line

Enable cluster mode by passing the `--cluster-mode` flag:

```bash
python main.py --cluster-mode
```

### Programmatic Usage

```python
from src.services.storage_service import StorageService
from src.core.connection_manager import ConnectionManager

# Initialize connections
connection_manager = ConnectionManager()
connection_manager.initialize(cluster_mode=True)

# Get connections
local_connection = connection_manager.get_clickhouse_connection()
cluster_connection = connection_manager.get_clickhouse_cluster_connection()

# Create storage service with cluster support
storage_service = StorageService(
    connection=local_connection,
    cluster_connection=cluster_connection,
    cluster_mode=True
)

# Transfer data - automatically creates both local and distributed tables
result = storage_service.transfer_temp_table_to_storage(
    temp_table_name="temp_results",
    result_id="job_123_results",
    period=period_obj
)

# Get distributed table name
if storage_service.distributed_processor:
    distributed_table = storage_service.distributed_processor.get_distributed_table_name(
        result["permanent_table"]
    )
    print(f"Distributed table: {distributed_table}")
```

## How It Works

1. **Local Table Creation**: Creates the standard local table using MergeTree engine
2. **Distributed Table Creation**: Automatically creates a distributed table with `_distributed` suffix
3. **Data Transfer**: Transfers data to both local and distributed tables
4. **Error Handling**: Distributed operations are optional - failures don't affect local table creation

## Table Naming Convention

- Local table: `kpi_results.data_{result_id}`
- Distributed table: `kpi_results.data_{result_id}_distributed`

## Distributed Table Engine

The distributed tables use the following engine configuration:

```sql
ENGINE = Distributed({cluster_name}, {database}, {table}, rand())
```

Where:
- `cluster_name`: From `CLICKHOUSE_CLUSTER_NAME` environment variable
- `database`: The database containing the local table
- `table`: The local table name
- `rand()`: Random sharding key for load distribution

## Benefits

1. **High Availability**: Data is available across multiple cluster nodes
2. **Load Distribution**: Queries can be distributed across cluster nodes
3. **Scalability**: Easy to scale reads across multiple nodes
4. **Transparency**: Applications can query either local or distributed tables

## Error Handling

- Distributed table creation failures don't affect local table creation
- Data transfer to distributed tables is optional
- Detailed logging for troubleshooting cluster issues
- Graceful fallback to local-only operation

## Monitoring

The service logs all cluster operations:

- Distributed table creation
- Data transfer to distributed tables
- Cluster connection status
- Error conditions and fallbacks

Check the logs for messages containing "distributed" or "cluster" for monitoring cluster operations.
