"""
Cluster storage processor for KPI results.

This module handles storage operations on ClickHouse cluster instances,
including cluster table management, remote data access, and cluster-specific operations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from src.core.clickhouse_connect import ClickHouseConnection
from src.models.axis import Period
from .base_storage_processor import BaseStorageProcessor


class ClusterStorageProcessor(BaseStorageProcessor):
    """
    Processor for ClickHouse cluster storage operations.
    
    Handles:
    - Cluster table creation and management
    - Remote data access using remote() function
    - Cluster-to-single-node data transfer
    - Cluster storage validation
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        cluster_connection: Optional[ClickHouseConnection] = None,
        msg_logger_func=None,
        progress_tracker=None,
    ):
        """
        Initialize the cluster storage processor.

        Args:
            connection: Single-node ClickHouse connection instance
            cluster_connection: Optional cluster ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
        """
        super().__init__(connection, msg_logger_func, progress_tracker)
        self.cluster_connection = cluster_connection
        self.logger = logging.getLogger(__name__)

    def create_cluster_permanent_table(
        self, table_name: str, structure: List[Dict[str, str]]
    ) -> bool:
        """
        Create permanent table on cluster with given structure.
        
        Args:
            table_name: Name of the table to create
            structure: List of column definitions with name and type
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.cluster_connection:
                self.logger.error("No cluster connection available")
                return False

            # Build column definitions
            column_defs = []
            dimension_cols = []

            for col in structure:
                col_name = col["name"].replace('"', "").replace("'", "").replace(" ", "_")
                col_type = col["type"]
                column_defs.append(f"`{col_name}` {col_type}")

                # Track dimension columns for ORDER BY
                if col_name.endswith("_position_number"):
                    dimension_cols.append(f"`{col_name}`")

            # Use dimensions for ORDER BY or fallback to tuple()
            order_by = ", ".join(dimension_cols) if dimension_cols else "tuple()"

            # Create table query
            create_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {", ".join(column_defs)}
                )
                ENGINE = MergeTree()
                ORDER BY ({order_by})
            """

            self.logger.info(f"Creating cluster permanent table: {table_name}")
            self.logger.debug(f"Create cluster table query: {create_query}")

            self.cluster_connection.execute_command(create_query)
            self.logger.info(f"Successfully created cluster permanent table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create cluster permanent table {table_name}: {str(e)}")
            return False

    def cluster_table_exists(self, table_name: str) -> bool:
        """Check if a table exists on the cluster connection."""
        try:
            if not self.cluster_connection:
                return False

            return self.table_exists(table_name, self.cluster_connection)

        except Exception as e:
            self.logger.error(f"Failed to check if cluster table {table_name} exists: {str(e)}")
            return False

    def transfer_to_cluster_storage(
        self,
        temp_table_name: str,
        result_id: str,
        period: Period,
        query_prefix: Optional[str] = "query_",
    ) -> Dict[str, Any]:
        """
        Transfer data from temporary table to cluster's job_result database.

        Args:
            temp_table_name: Name of the temporary table (without database prefix)
            result_id: Combined result ID for the job
            period: Period information
            query_prefix: Prefix for query IDs

        Returns:
            Dictionary with transfer status information
        """
        try:
            self.logger.info(
                f"Transferring temp table {temp_table_name} to cluster storage (job_result database) for period {period.label}"
            )

            if not self.cluster_connection:
                error_msg = "No cluster connection available"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Check if temporary table exists on cluster connection
            if not self.table_exists(temp_table_name, self.cluster_connection):
                error_msg = f"Temporary table {temp_table_name} does not exist on cluster"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Get table structure from temporary table
            table_structure = self.get_table_structure(temp_table_name, self.cluster_connection)
            if not table_structure:
                error_msg = f"Failed to get structure of temporary table {temp_table_name}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Define cluster table in job_result database
            cluster_table = f"job_result.data_{result_id}"

            # Check if cluster table exists, create if not
            if not self.cluster_table_exists(cluster_table):
                if not self.create_cluster_permanent_table(cluster_table, table_structure):
                    error_msg = f"Failed to create cluster permanent table {cluster_table}"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Transfer data from temp table to cluster permanent table
            rows_transferred, transfer_query_id, error = self.transfer_data(
                temp_table_name, cluster_table, query_prefix, self.cluster_connection
            )

            if error:
                return {"success": False, "error": error, "rows_transferred": 0}

            self.logger.info(
                f"Successfully transferred {rows_transferred} rows from {temp_table_name} to cluster storage"
            )

            return {
                "success": True,
                "error": None,
                "rows_transferred": rows_transferred,
                "permanent_table": cluster_table,
                "transfer_query_id": transfer_query_id,
                "storage_location": "cluster",
            }

        except Exception as e:
            error_msg = f"Failed to transfer temp table {temp_table_name} to cluster storage: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def get_cluster_table_structure(
        self, table_name: str, cluster_name: str, database: str
    ) -> Optional[List[Dict[str, str]]]:
        """
        Get table structure from cluster using remote() function.

        Args:
            table_name: Name of the table on the cluster
            cluster_name: Name of the cluster
            database: Database name on the cluster

        Returns:
            List of column definitions or None if failed
        """
        try:
            # Use remote() function to access cluster table structure
            remote_query = f"""
                SELECT name, type 
                FROM remote('{cluster_name}', 'system.columns') 
                WHERE database = '{database}' AND table = '{table_name}'
                ORDER BY position
            """

            self.logger.debug(f"Getting cluster table structure: {remote_query}")
            result = self.connection.get_query_dataframe(remote_query)

            if result.empty:
                self.logger.warning(f"No structure found for cluster table {database}.{table_name}")
                return None

            structure = []
            for _, row in result.iterrows():
                structure.append({"name": row["name"], "type": row["type"]})

            self.logger.info(
                f"Retrieved structure for cluster table {database}.{table_name}: {len(structure)} columns"
            )
            return structure

        except Exception as e:
            self.logger.error(f"Failed to get cluster table structure for {table_name}: {str(e)}")
            return None

    def transfer_cluster_to_single_node(
        self,
        cluster_table_name: str,
        result_id: str,
        period: Period,
        query_prefix: Optional[str] = "query_",
        remote_cluster_name: Optional[str] = None,
        remote_database: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Transfer KPI results from cluster-generated distributed tables to a centralized single-node database.

        Args:
            cluster_table_name: Name of the table on the cluster (without database prefix)
            result_id: Combined result ID for the job
            period: Period information
            query_prefix: Prefix for query IDs
            remote_cluster_name: Name of the remote cluster (defaults to config value)
            remote_database: Database name on remote cluster (defaults to config value)

        Returns:
            Dictionary with transfer status information
        """
        try:
            from src.core.config import config

            # Use config defaults if not provided
            if not remote_cluster_name:
                remote_cluster_name = config.clickhouse_cluster_name
            if not remote_database:
                remote_database = config.clickhouse_cluster_database

            if not remote_cluster_name:
                error_msg = "Cluster name not configured for remote table access"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            self.logger.info(
                f"Transferring cluster results from {cluster_table_name} to single-node storage for period {period.label}"
            )

            # Get table structure from cluster using remote() function
            cluster_table_structure = self.get_cluster_table_structure(
                cluster_table_name, remote_cluster_name, remote_database
            )
            if not cluster_table_structure:
                error_msg = f"Failed to get structure of cluster table {cluster_table_name}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Create permanent table on single-node storage
            permanent_table = f"kpi_results.data_{result_id}"
            if not self.table_exists(permanent_table):
                from .local_storage_processor import LocalStorageProcessor
                local_processor = LocalStorageProcessor(self.connection, self.msg_logger_func, self.progress_tracker)
                if not local_processor.create_permanent_table(permanent_table, cluster_table_structure):
                    error_msg = f"Failed to create permanent table {permanent_table}"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Transfer data from cluster to single-node using remote() function
            rows_transferred, transfer_query_id, error = self._transfer_cluster_data_to_single_node(
                cluster_table_name,
                permanent_table,
                query_prefix,
                remote_cluster_name,
                remote_database,
            )

            if error:
                return {"success": False, "error": error, "rows_transferred": 0}

            self.logger.info(
                f"Successfully transferred {rows_transferred} rows from cluster table {cluster_table_name} to single-node storage"
            )

            return {
                "success": True,
                "error": None,
                "rows_transferred": rows_transferred,
                "permanent_table": permanent_table,
                "cluster_source_table": f"{remote_database}.{cluster_table_name}",
                "transfer_query_id": transfer_query_id,
                "transfer_type": "cluster_to_single_node",
            }

        except Exception as e:
            error_msg = f"Failed to transfer cluster results from {cluster_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def _transfer_cluster_data_to_single_node(
        self,
        cluster_table_name: str,
        target_table: str,
        query_prefix: str,
        cluster_name: str,
        database: str,
    ) -> Tuple[Optional[int], Optional[str], Optional[str]]:
        """
        Transfer data from cluster table to single-node table using remote() function.

        Args:
            cluster_table_name: Source table name on cluster
            target_table: Target table name on single-node
            query_prefix: Prefix for query IDs
            cluster_name: Name of the cluster
            database: Database name on cluster

        Returns:
            Tuple of (rows_transferred, query_id, error_message)
        """
        try:
            # Build remote query to select data from cluster
            remote_select_query = f"""
                SELECT * FROM remote('{cluster_name}', '{database}.{cluster_table_name}')
            """

            # Generate unique query ID for tracking
            query_id = self.generate_query_id(f"{query_prefix}cluster_transfer_")
            settings = {"query_id": query_id}

            self.logger.info(
                f"Transferring data from cluster table {database}.{cluster_table_name} to {target_table}"
            )
            self.logger.debug(f"Remote query: {remote_select_query}")

            # Use the single-node connection to execute the remote query and insert into target table
            if not self.connection.add_rows_to_table(
                target_table, source_query=remote_select_query, settings=settings
            ):
                error_msg = f"Failed to transfer data from cluster table {cluster_table_name} to {target_table}"
                self.logger.error(error_msg)
                return None, None, error_msg

            # Count transferred rows by querying the cluster table
            count_query = f"""
                SELECT count() as cnt FROM remote('{cluster_name}', '{database}.{cluster_table_name}')
            """
            result = self.connection.get_query_dataframe(count_query)
            row_count = result.iloc[0]["cnt"] if not result.empty else 0

            self.logger.info(f"Successfully transferred {row_count} rows from cluster to single-node")
            return row_count, query_id, None

        except Exception as e:
            error_msg = f"Failed to transfer cluster data: {str(e)}"
            self.logger.error(error_msg)
            return None, None, error_msg

    def verify_cluster_table_exists(
        self, table_name: str, cluster_name: str, database: str
    ) -> bool:
        """
        Verify that a table exists on the cluster using remote() function.

        Args:
            table_name: Name of the table to check
            cluster_name: Name of the cluster
            database: Database name on cluster

        Returns:
            True if table exists, False otherwise
        """
        try:
            # Use remote() function to check if table exists on cluster
            check_query = f"""
                SELECT 1 FROM remote('{cluster_name}', 'system.tables')
                WHERE database = '{database}' AND name = '{table_name}'
                LIMIT 1
            """

            result = self.connection.get_query_dataframe(check_query)
            exists = not result.empty

            self.logger.debug(f"Cluster table {database}.{table_name} exists: {exists}")
            return exists

        except Exception as e:
            self.logger.error(f"Failed to verify cluster table existence for {table_name}: {str(e)}")
            return False

    def get_cluster_table_row_count(
        self,
        table_name: str,
        cluster_name: Optional[str] = None,
        database: Optional[str] = None,
    ) -> int:
        """
        Get row count from a cluster table using remote() function.

        Args:
            table_name: Name of the table on cluster
            cluster_name: Name of the cluster (defaults to config value)
            database: Database name on cluster (defaults to config value)

        Returns:
            Number of rows in the cluster table
        """
        try:
            from src.core.config import config

            # Use config defaults if not provided
            if not cluster_name:
                cluster_name = config.clickhouse_cluster_name
            if not database:
                database = config.clickhouse_cluster_database

            if not cluster_name:
                self.logger.warning("Cluster name not configured")
                return 0

            count_query = f"""
                SELECT count() as cnt FROM remote('{cluster_name}', '{database}.{table_name}')
            """

            result = self.connection.get_query_dataframe(count_query)
            row_count = result.iloc[0]["cnt"] if not result.empty else 0

            self.logger.debug(f"Cluster table {database}.{table_name} has {row_count} rows")
            return row_count

        except Exception as e:
            self.logger.error(f"Failed to get cluster table row count for {table_name}: {str(e)}")
            return 0