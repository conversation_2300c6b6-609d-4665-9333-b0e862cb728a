"""
Base storage processor for common storage operations.

This module provides a base class for all storage processors with common functionality.
"""

import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple

from src.core.clickhouse_connect import ClickHouseConnection
from src.services.base_service import BaseService


class BaseStorageProcessor(BaseService):
    """
    Base class for all storage processors.
    
    Provides common functionality for table operations, data validation,
    and error handling across different storage types.
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        msg_logger_func=None,
        progress_tracker=None,
    ):
        """
        Initialize the base storage processor.

        Args:
            connection: ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
        """
        super().__init__(msg_logger_func)
        self.connection = connection
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(__name__)

    def table_exists(self, table_name: str, connection: Optional[ClickHouseConnection] = None) -> bool:
        """Check if a table exists."""
        try:
            if not connection:
                connection = self.connection

            # Parse table name and optional database name
            if "." in table_name:
                database, table = table_name.split(".", 1)
            else:
                table = table_name
                database = None

            query = f"""
                SELECT 1 FROM system.tables
                WHERE name = '{table}'
            """
            if database:
                query += f" AND database = '{database}'"

            result = connection.get_query_dataframe(query)
            return not result.empty

        except Exception as e:
            self.logger.error(f"Failed to check if table {table_name} exists: {str(e)}")
            return False

    def get_table_structure(
        self, table_name: str, connection: Optional[ClickHouseConnection] = None
    ) -> Optional[List[Dict[str, str]]]:
        """Get table structure information."""
        try:
            if not connection:
                connection = self.connection

            query = f"DESCRIBE TABLE {table_name}"
            result = connection.get_query_dataframe(query)

            if result.empty:
                return None

            structure = []
            for _, row in result.iterrows():
                structure.append({"name": row["name"], "type": row["type"]})

            return structure

        except Exception as e:
            self.logger.error(f"Failed to get structure of table {table_name}: {str(e)}")
            return None

    def count_table_rows(self, table_name: str, connection: Optional[ClickHouseConnection] = None) -> int:
        """Count rows in a table."""
        try:
            if not connection:
                connection = self.connection
                
            query = f"SELECT count() as cnt FROM {table_name}"
            result = connection.get_query_dataframe(query)
            return result.iloc[0]["cnt"] if not result.empty else 0

        except Exception as e:
            self.logger.error(f"Failed to count rows in table {table_name}: {str(e)}")
            return 0

    def escape_sql_string(self, value: str) -> str:
        """Escape a string for safe SQL insertion."""
        if value is None:
            return ""

        # Convert to string if not already
        str_value = str(value)

        # Escape single quotes by doubling them
        escaped = str_value.replace("'", "''")

        # Escape backslashes
        escaped = escaped.replace("\\", "\\\\")

        return escaped

    def generate_query_id(self, prefix: str = "query_") -> str:
        """Generate a unique query ID."""
        return f"{prefix}{uuid.uuid4().hex[:8]}"

    def transfer_data(
        self,
        source_table: str,
        target_table: str,
        query_prefix: Optional[str] = "query_",
        connection: Optional[ClickHouseConnection] = None,
    ) -> Tuple[Optional[int], Optional[str], Optional[str]]:
        """
        Transfer data between tables and return row count.

        Args:
            source_table: Source table name
            target_table: Target table name
            query_prefix: Prefix for query IDs
            connection: Optional connection to use

        Returns:
            Tuple of (rows_transferred, query_id, error_message)
        """
        try:
            if not connection:
                connection = self.connection

            # Use SELECT query to transfer data
            insert_query = f"SELECT * FROM {source_table}"
            settings = {"query_id": self.generate_query_id(query_prefix)}

            self.logger.info(f"Transferring data from {source_table} to {target_table}")
            
            if not connection.add_rows_to_table(
                target_table, source_query=insert_query, settings=settings
            ):
                error_msg = f"Failed to transfer data from {source_table} to {target_table}"
                self.logger.error(error_msg)
                return None, None, error_msg

            # Count transferred rows
            row_count = self.count_table_rows(source_table, connection)

            # Drop source table
            connection.drop_temp_table(source_table)

            self.logger.info(f"Transferred {row_count} rows from {source_table}")
            return row_count, settings["query_id"], None

        except Exception as e:
            error_msg = f"Failed to transfer data from {source_table} to {target_table}: {str(e)}"
            self.logger.error(error_msg)
            return None, None, error_msg