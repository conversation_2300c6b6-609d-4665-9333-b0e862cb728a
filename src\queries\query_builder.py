"""
SQL query builder for ClickHouse.

This module provides a query builder for ClickHouse with improved
template handling, query validation, and error handling.
"""

import logging
from typing import Dict, List, Optional

from src.utils.sql_template_manager import SQLTemplateManager
from src.models.axis import AxisData, FilterData, FactsData, Period, SUFactData

# Set up logging
logger = logging.getLogger(__name__)


class ClickHouseQuery:
    """
    Query builder for ClickHouse.

    This class provides methods for building SQL queries for ClickHouse
    with improved template handling, query validation, and error handling.
    """

    def __init__(
        self,
        period: Optional[Period] = None,
        product_group: Optional[str] = None,
        filters: Optional[Dict[str, FilterData]] = None,
        axes: Optional[Dict[str, AxisData]] = None,
        id_panel: int = 1,
        template_dir: str = "./src/queries/templates",
        su_fact: Optional[SUFactData] = None,
        facts_axis: Optional[List[FactsData]] = None,
        required_facts: Optional[List[str]] = None,
        cluster_mode: Optional[bool] = None,
        cluster_config: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize the query builder.

        Args:
            period: Period model containing start and end dates
            filters: Dictionary of pre-validated filters as FilterData models
            axes: Dictionary of pre-validated axes as AxisData models
            id_panel: Panel ID
            split_axis: Axis to split by
            template_dir: Directory containing SQL templates
            su_fact: Optional formula for SU (Standard Unit) calculation
            facts_axis: Optional list of validated FactsData models
            required_facts: List of required facts for templates
        """
        # Initialize logger
        self.logger = logging.getLogger(__name__)

        # Store cluster configuration
        self.cluster_mode = cluster_mode
        self._cluster_config = cluster_config or {}

        # Determine template directory based on cluster mode
        # if cluster_mode:
        #     template_dir = "./src/queries/templates_cluster"
        # else:
        template_dir = (
            "./src/queries/templates_v1"
            if not cluster_mode
            else "./src/queries/templates_cluster"
        )

        # Initialize template manager
        self.template_manager = SQLTemplateManager(template_dir=template_dir)

        # Set the period property
        self._period = period

        self.product_group = product_group
        self.id_panel = id_panel
        self.su_fact = su_fact

        # Store facts_axis directly as models
        self.facts_axis = facts_axis

        # Initialize default values for filters and axes if not provided
        default_filters = {}

        default_axes = {}

        # Use provided values or defaults
        self.filters = filters or default_filters
        self.axes = axes or default_axes
        self.required_facts = required_facts or []

        # Log the initialization
        self.logger.info(
            f"ClickHouseQuery initialized with {len(self.axes)} axes, {len(self.filters)} filters, {len(self.required_facts)} required facts"
        )
        if self.cluster_mode:
            self.logger.info("Cluster mode enabled - using ID-based object resolution")
        if self.facts_axis:
            self.logger.info(f"Facts axis count: {len(self.facts_axis)}")
        if self.su_fact:
            self.logger.info(f"SU fact: {self.su_fact}")

    # Property getters and setters

    @property
    def period(self) -> Optional[Period]:
        """Get the period model."""
        return self._period

    @period.setter
    def period(self, value: Period) -> None:
        """Set the period model."""
        if value != self._period:
            self._period = value

    @property
    def cluster_config(self) -> Optional[Dict[str, str]]:
        """Get the cluster configuration."""
        return self._cluster_config

    @cluster_config.setter
    def cluster_config(self, value: Dict[str, str]) -> None:
        """Set the cluster configuration."""
        if value != self._cluster_config:
            self._cluster_config = value

    # Query methods
    def query_pre_axis(
        self,
        catman: bool = False,
        json_mode: bool = False,
    ) -> str:
        """
        Generate SQL query for pre-axis using the pre-axis template.

        Args:
            catman: Whether to use the Catman pre-axis template

        Returns:
            Rendered SQL query for pre-axis

        Raises:
            QueryError: If template rendering fails
        """
        context = {
            "period": self.period,
            "product_group": ",".join(f"'{p}'" for p in self.product_group)
            if self.product_group
            else None,
            "period_start": self.period.date_start if self.period else None,
            "period_end": self.period.date_end if self.period else None,
            "filters": self.filters,
            "axes": self.axes,
            "id_panel": self.id_panel,
            "su_fact": self.su_fact,
            "facts_axis": self.facts_axis,
            "required_facts": self.required_facts,
            "catman": catman,
            "json_mode": json_mode,
        }

        # Add cluster-specific context if in cluster mode
        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template("pre-axis.jinja", context)

    def query_axis(self) -> str:
        """
        Generate SQL query for axis temporary table using the axis template.

        Returns:
            Rendered SQL query for axis temporary table

        Raises:
            QueryError: If template rendering fails
        """
        context = {
            "axes": self.axes,
            "facts_axis": self.facts_axis,
            "required_facts": self.required_facts,
            "period_start": self.period.date_start if self.period else None,
            "period_end": self.period.date_end if self.period else None,
            "id_panel": self.id_panel,
        }

        # Add cluster-specific context if in cluster mode
        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template("axis.jinja", context)

    def query_buyers(self, catman: bool = False) -> str:
        """
        Generate SQL query for buyers using the buyers template.

        Returns:
            Rendered SQL query for buyers

        Raises:
            QueryError: If template rendering fails
        """
        template_name = "buyers_catman.jinja" if catman else "buyers.jinja"
        context = {
            "axes": self.axes,
            "period_start": self.period.date_start if self.period else None,
            "period_end": self.period.date_end if self.period else None,
            "id_panel": self.id_panel,
            "facts_axis": self.facts_axis,
            "required_facts": self.required_facts,
        }

        # Add cluster-specific context if in cluster mode
        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template(template_name, context)

    def query_buf(self, catman: bool = False) -> str:
        """
        Generate SQL query for buf temporary table using the buf template.

        Returns:
            Rendered SQL query for buf temporary table

        Raises:
            QueryError: If template rendering fails
        """
        context = {
            "axes": self.axes,
            "filters": self.filters,
            "facts_axis": self.facts_axis,
            "catman": catman,
        }

        # Add cluster-specific context if in cluster mode
        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template("buf.jinja", context)

    def query_final(
        self,
        labels: bool = True,
        catman: bool = False,
        json_mode: bool = False,
    ) -> str:
        """
        Generate SQL query for kpi using the kpi template.

        Args:
            labels: Whether to include labels in the query
            catman: Whether to use the Catman KPI template

        Returns:
            Rendered SQL query for final temporary table

        Raises:
            QueryError: If template rendering fails
        """
        # Add debug logging before rendering
        for axis_key, axis_data in self.axes.items():
            if axis_data.type == "axsh":
                ddl_queries = axis_data.ddl.get("queries", [])
                self.logger.info(
                    f"DEBUG: axis {axis_key} ddl length = {len(ddl_queries)}"
                )

        template_name = "catman_kpi.jinja" if catman else "standard_kpi.jinja"

        context = {
            "period": self.period,
            "period_start": self.period.date_start if self.period else None,
            "period_end": self.period.date_end if self.period else None,
            "filters": self.filters,
            "axes": self.axes,
            "id_panel": self.id_panel,
            "labels": labels,
            "su_fact": self.su_fact,
            "facts_axis": self.facts_axis,
            "required_facts": self.required_facts,
            "catman": catman,
            "json_mode": json_mode,
        }

        # Add cluster-specific context if in cluster mode
        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template(template_name, context)

    def query_kpi(
        self,
        labels: bool = True,
        position_numbers: bool = True,
        catman: bool = False,
    ) -> str:
        """
        Generate SQL query for kpi using the kpi template.

        Args:
            labels: Whether to include labels in the query
            position_numbers: Whether to include position numbers in the query
            period: Period model containing start and end dates

        Returns:
            Rendered SQL query for kpi

        Raises:
            QueryError: If template rendering fails
        """
        # Add debug logging before rendering

        context = {
            "period": self.period,
            "axes": self.axes,
            "labels": labels,
            "facts_axis": self.facts_axis,
            "position_numbers": position_numbers,
            "catman": catman,
        }

        if self.cluster_mode and self.cluster_config:
            context.update(
                {
                    "cluster_name": self.cluster_config.get("cluster_name", ""),
                    "cluster_database": self.cluster_config.get("database", ""),
                    "table_suffix": self.cluster_config.get("table_suffix", ""),
                }
            )

        return self.template_manager.render_template("final_kpi.jinja", context)

    # Cluster-specific query methods
    def query_cluster_pre_axis_replicated(self, catman: bool = False) -> str:
        """Generate CREATE REPLICATED TABLE query for pre-axis in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context(catman=catman)
        return self.template_manager.render_template(
            "pre-axis_replicated.jinja", context
        )

    def query_cluster_axis_replicated(self) -> str:
        """Generate CREATE REPLICATED TABLE query for axis in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context()
        return self.template_manager.render_template("axis_replicated.jinja", context)

    def query_cluster_buyers_replicated(self, catman: bool = False) -> str:
        """Generate CREATE REPLICATED TABLE query for buyers in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context(catman=catman)
        return self.template_manager.render_template("buyers_replicated.jinja", context)

    def query_cluster_buf_replicated(self, catman: bool = False) -> str:
        """Generate CREATE REPLICATED TABLE query for buf in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context(catman=catman)
        return self.template_manager.render_template("buf_replicated.jinja", context)

    def query_cluster_final_replicated(self, catman: bool = False) -> str:
        """Generate CREATE REPLICATED TABLE query for final in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context(catman=catman)
        template_name = (
            "catman_kpi_replicated.jinja" if catman else "standard_kpi_replicated.jinja"
        )
        return self.template_manager.render_template(template_name, context)

    def query_cluster_final_kpi_replicated(
        self, labels: bool = True, position_numbers: bool = True, catman: bool = False
    ) -> str:
        """Generate CREATE REPLICATED TABLE query for final KPI in cluster mode."""
        if not self.cluster_mode:
            raise ValueError("Cluster mode is not enabled")

        context = self._get_cluster_context(
            labels=labels, position_numbers=position_numbers, catman=catman
        )
        return self.template_manager.render_template(
            "final_kpi_replicated.jinja", context
        )

    def _get_cluster_context(self, **kwargs) -> Dict:
        """Get common context for cluster templates."""
        context = {
            "period": self.period,
            "product_group": ",".join(f"'{p}'" for p in self.product_group)
            if self.product_group
            else None,
            "period_start": self.period.date_start if self.period else None,
            "period_end": self.period.date_end if self.period else None,
            "filters": self.filters,
            "axes": self.axes,
            "id_panel": self.id_panel,
            "su_fact": self.su_fact,
            "facts_axis": self.facts_axis,
            "required_facts": self.required_facts,
            "cluster_name": self.cluster_config.get("cluster_name", ""),
            "cluster_database": self.cluster_config.get("database", ""),
            "table_suffix": self.cluster_config.get("table_suffix", ""),
        }
        context.update(kwargs)
        return context
