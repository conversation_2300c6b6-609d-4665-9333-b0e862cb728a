"""
Cluster API client for object resolution.

This module provides functionality to resolve object definitions and DDL queries
from the cluster API endpoints when in cluster mode.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from src.core.config import config

try:
    import aiohttp

    AIOHTTP_AVAILABLE = True
    ClientSession = aiohttp.ClientSession
    ClientTimeout = aiohttp.ClientTimeout
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None

    # Create dummy classes for type annotations
    class ClientSession:
        pass

    class ClientTimeout:
        pass


@dataclass
class ObjectDefinition:
    """Object definition resolved from cluster API."""

    id: int
    name: str
    type: str
    client_group: str
    author: str
    last_update: str
    position_list: List[Dict[str, Any]]
    expression: Optional[str] = None
    dependencies_id: Optional[List[int]] = None


@dataclass
class DDLTranslation:
    """DDL translation resolved from cluster API."""

    cte: str
    queries: Dict[str, str]  # position_number -> query
    dependencies_id: Optional[List[int]] = None


class ClusterAPIClient:
    """
    Client for interacting with cluster API endpoints to resolve object definitions and DDL queries.

    This client handles:
    - Object description resolution via /v1/json_object/object/{id}/description
    - DDL query translation via /v1/json_object/axis/{id}/translation/{engine_type}
    - Caching of resolved objects to avoid repeated API calls
    - Error handling and fallback mechanisms
    """

    def __init__(self, base_url: Optional[str] = None, timeout: int = 30):
        """
        Initialize the cluster API client.

        Args:
            base_url: Base URL for the cluster API (defaults to config.job_api_url)
            timeout: Request timeout in seconds
        """
        self.logger = logging.getLogger(__name__)

        if not AIOHTTP_AVAILABLE:
            self.logger.error(
                "aiohttp is not available. Cluster API client will not function."
            )
            raise ImportError(
                "aiohttp is required for cluster API client functionality"
            )

        self.base_url = base_url or config.job_api_url
        self.timeout = timeout

        # Cache for resolved objects to avoid repeated API calls
        self._object_cache: Dict[int, ObjectDefinition] = {}
        self._ddl_cache: Dict[Tuple[int, str], DDLTranslation] = {}

        # Session for connection pooling
        self._session: Optional[ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self._session = ClientSession(timeout=ClientTimeout(total=self.timeout))
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()

    async def _get_session(self) -> ClientSession:
        """Get or create HTTP session."""
        if not self._session:
            self._session = ClientSession(timeout=ClientTimeout(total=self.timeout))
        return self._session

    async def resolve_object_description(
        self, object_id: int
    ) -> Optional[ObjectDefinition]:
        """
        Resolve object description from cluster API.

        Args:
            object_id: Object ID to resolve

        Returns:
            ObjectDefinition if successful, None otherwise
        """
        # Check cache first
        if object_id in self._object_cache:
            self.logger.debug(f"Using cached object definition for ID {object_id}")
            return self._object_cache[object_id]

        try:
            session = await self._get_session()
            url = f"{self.base_url}/v1/json_object/object/{object_id}/description"

            self.logger.debug(
                f"Resolving object description for ID {object_id} from {url}"
            )

            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()

                    # Extract values from main json
                    client_group = data.get("client_group") or "Unknown"
                    author = data.get("author") or "Unknown"
                    name = data.get("name") or data.get("axis_name") or data.get("filter_name") or "Unknown"
                    obj_type = data.get("type") or data.get("axis_type") or data.get("filter_type") or "Unknown"
                    obj_expression = data.get("expression") or ""

                    # Extract definition_json
                    definition_json = data.get("definition_json", {})
                    if not definition_json:
                        self.logger.error(
                            f"No definition_json found for object ID {object_id}"
                        )
                        return None
                    

                    # Extract required fields
                    position_list = definition_json.get("position_list", [])
                    last_update = definition_json.get("last_update")

                    if not name or not obj_type:
                        self.logger.error(
                            f"Missing required fields (name/type) for object ID {object_id}"
                        )
                        return None

                    # Create object definition
                    obj_def = ObjectDefinition(
                        id=object_id,
                        name=name,
                        type=obj_type,
                        position_list=position_list,
                        client_group=client_group,
                        author=author,
                        last_update=last_update,
                        expression=obj_expression,
                    )

                    # Cache the result
                    self._object_cache[object_id] = obj_def

                    self.logger.info(
                        f"Resolved object ID {object_id}: {name} (type: {obj_type}, positions: {len(position_list)})"
                    )
                    return obj_def

                else:
                    self.logger.error(
                        f"Failed to resolve object ID {object_id}: HTTP {response.status}"
                    )
                    return None

        except Exception as e:
            self.logger.error(
                f"Error resolving object description for ID {object_id}: {e}",
                exc_info=True,
            )
            return None

    async def resolve_ddl_translation(
        self,
        odject_id: int,
        object_type: str,
        engine_type: str = "clickhouse",
        cluster: bool = False,
        id_panel: int = 1,
    ) -> Optional[DDLTranslation]:
        """
        Resolve DDL translation from cluster API.

        Args:
            object_id: Object ID to resolve DDL for
            engine_type: Engine type (default: "clickhouse")

        Returns:
            DDLTranslation if successful, None otherwise
        """
        cache_key = (odject_id, engine_type)

        # Check cache first
        if cache_key in self._ddl_cache:
            self.logger.debug(f"Using cached DDL translation for axis ID {odject_id}")
            return self._ddl_cache[cache_key]

        try:
            session = await self._get_session()
            if object_type == "axis":
                url = f"{self.base_url}/v1/json_object/axis/{odject_id}/translation/{engine_type}?cluster={cluster}&id_panel={id_panel}"
            elif object_type == "filter":
                url = f"{self.base_url}/v1/json_object/filter/{odject_id}/translation/{engine_type}?cluster={cluster}&id_panel={id_panel}"
            else:
                url = f"{self.base_url}/v1/json_object/object/{odject_id}/translation/{engine_type}?cluster={cluster}&id_panel={id_panel}"

            self.logger.debug(
                f"Resolving DDL translation for object ID {odject_id} from {url}"
            )

            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()

                    # Extract required fields
                    cte = data.get("cte", "")
                    queries = data.get("queries", {}) if data.get("queries") else {1: data.get("query", "")}
                    dependencies_id = data.get("dependencies_id")

                    # Create DDL translation
                    ddl_translation = DDLTranslation(
                        cte=cte, queries=queries, dependencies_id=dependencies_id
                    )

                    # Cache the result
                    self._ddl_cache[cache_key] = ddl_translation

                    self.logger.info(
                        f"Resolved DDL for {object_type} ID {odject_id}: {len(queries)} queries"
                    )
                    return ddl_translation

                else:
                    self.logger.error(
                        f"Failed to resolve DDL for {object_type} ID {odject_id}: HTTP {response.status}"
                    )
                    return None

        except Exception as e:
            self.logger.error(
                f"Error resolving DDL translation for {object_type} ID {odject_id}: {e}",
                exc_info=True,
            )
            return None

    async def resolve_multiple_objects(
        self, object_ids: List[int]
    ) -> Dict[int, ObjectDefinition]:
        """
        Resolve multiple object descriptions concurrently.

        Args:
            object_ids: List of object IDs to resolve

        Returns:
            Dictionary mapping object ID to ObjectDefinition
        """
        if not object_ids:
            return {}

        self.logger.info(f"Resolving {len(object_ids)} objects concurrently")

        # Create tasks for concurrent resolution
        tasks = [self.resolve_object_description(obj_id) for obj_id in object_ids]

        # Execute tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        resolved_objects = {}
        for obj_id, result in zip(object_ids, results):
            if isinstance(result, Exception):
                self.logger.error(f"Failed to resolve object ID {obj_id}: {result}")
            elif result is not None:
                resolved_objects[obj_id] = result

        self.logger.info(
            f"Successfully resolved {len(resolved_objects)}/{len(object_ids)} objects"
        )
        return resolved_objects

    async def resolve_multiple_ddl(
        self,
        objects: Dict[int, str],
        engine_type: str = "ClickHouse",
        cluster: bool = False,
        id_panel: int = 1,
    ) -> Dict[int, DDLTranslation]:
        """
        Resolve multiple DDL translations concurrently.

        Args:
            objects: Dictionary of object IDs to object types to resolve DDL for
            engine_type: Engine type (default: "clickhouse")

        Returns:
            Dictionary mapping object ID to DDLTranslation
        """
        if not objects:
            return {}

        self.logger.info(f"Resolving DDL for {len(objects)} objects concurrently")

        # Create tasks for concurrent resolution
        tasks = [
            self.resolve_ddl_translation(
                object_id,
                object_type,
                engine_type,
                cluster,
                id_panel,
            )
            for object_id, object_type in objects.items()
        ]

        # Execute tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        resolved_ddl = {}
        for object_id, result in zip(objects, results):
            if isinstance(result, Exception):
                self.logger.error(
                    f"Failed to resolve DDL for object ID {object_id}: {result}"
                )
            elif result is not None:
                resolved_ddl[object_id] = result

        self.logger.info(
            f"Successfully resolved DDL for {len(resolved_ddl)}/{len(objects)} objects"
        )
        return resolved_ddl

    def clear_cache(self):
        """Clear all cached objects and DDL translations."""
        self._object_cache.clear()
        self._ddl_cache.clear()
        self.logger.info("Cleared cluster API cache")

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            "objects_cached": len(self._object_cache),
            "ddl_cached": len(self._ddl_cache),
        }
