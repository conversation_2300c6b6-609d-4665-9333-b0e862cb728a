# Storage Service Architecture

This document describes the modular storage service architecture that uses specialized processors to handle different types of storage operations in ClickHouse environments.

## Overview

The architecture splits storage operations into specialized processors, each handling a specific type of storage operation. This provides better separation of concerns, improved maintainability, easier testing, and optimized performance for each storage type.

## Architecture Components

### Storage Service (`StorageService`)

The main orchestrator that manages different storage processors and provides a unified interface for all storage operations.

**Key Features:**
- Intelligent routing based on storage mode configuration
- Unified interface for all storage operations
- Support for local, cluster, and distributed storage modes
- Modular processor-based architecture
- Comprehensive error handling and validation
- Optimized performance for each storage type

### Storage Processors

#### 1. Local Storage Processor (`LocalStorageProcessor`)

Handles storage operations on the local ClickHouse instance.

**Responsibilities:**
- Local table creation and management
- Data transfer from temporary to permanent tables
- Local storage validation and cleanup

#### 2. Cluster Storage Processor (`ClusterStorageProcessor`)

Handles storage operations on ClickHouse cluster instances.

**Responsibilities:**
- Cluster table creation and management
- Remote data access using remote() function
- Cluster-to-single-node data transfer
- Cluster storage validation

#### 3. Distributed Storage Processor (`DistributedStorageProcessor`)

Handles distributed table operations for ClickHouse cluster environments.

**Responsibilities:**
- Distributed table creation and management
- Data replication across cluster nodes
- High availability storage operations
- Distributed table validation

#### 4. Metadata Storage Processor (`MetadataStorageProcessor`)

Handles metadata storage operations including job metadata management.

**Responsibilities:**
- Job metadata creation and updates
- Status tracking and management
- Metadata table operations
- Job information serialization

## Usage Examples

### Using StorageService (Recommended)

```python
from src.services.storage_service import StorageService

# Initialize storage service
storage_service = StorageService(
    connection=connection,
    cluster_connection=cluster_connection,
    cluster_mode=False,
    cluster_storage_mode=False,
)

# Initialize job metadata
result_id = storage_service.initialize_job_metadata(
    job_parameters=job_parameters,
    username="user"
)

# Transfer data to storage
result = storage_service.transfer_temp_table_to_storage(
    temp_table_name="temp_table",
    result_id=result_id,
    period=period
)

# Finalize job
storage_service.finalize_job_storage(
    job_parameters=job_parameters,
    status="done"
)
```

### Using Individual Processors

```python
from src.processors.storage import LocalStorageProcessor, MetadataStorageProcessor

# Use local storage processor
local_processor = LocalStorageProcessor(connection)
exists = local_processor.table_exists("table_name")

# Use metadata processor
metadata_processor = MetadataStorageProcessor(connection)
status = metadata_processor.get_job_status("result_id")
```

## Storage Modes

### Local Storage Mode
- Results stored locally on single-node server
- Uses `LocalStorageProcessor` and `MetadataStorageProcessor`
- Default mode for single-node deployments

### Cluster Mode
- Results stored locally with distributed tables on cluster
- Uses `LocalStorageProcessor`, `DistributedStorageProcessor`, and `MetadataStorageProcessor`
- Provides high availability through distributed tables

### Cluster Storage Mode
- Results stored directly on cluster's job_result database
- Uses `ClusterStorageProcessor` and `MetadataStorageProcessor`
- Centralized storage on cluster infrastructure

## Implementation Benefits

### Architecture Advantages

1. **Optimized Performance**: Each processor is specialized and optimized for its specific storage type
2. **Clear Separation of Concerns**: Each processor has well-defined responsibilities
3. **Enhanced Maintainability**: Modular design makes code easier to understand and modify
4. **Improved Testing**: Individual processors can be tested in isolation
5. **Extensibility**: Easy to add new storage types or modify existing ones
6. **Error Isolation**: Issues in one processor don't affect others

### Design Principles

- **Single Responsibility**: Each processor handles one type of storage operation
- **Dependency Injection**: Processors receive their dependencies through constructors
- **Interface Segregation**: Clean interfaces between processors and the storage service
- **Open/Closed Principle**: Easy to extend with new processors without modifying existing code

## Configuration

Storage behavior is controlled by configuration parameters:

- `cluster_mode`: Enable distributed table creation alongside local storage
- `cluster_storage_mode`: Store results directly on cluster's job_result database
- `cluster_connection`: ClickHouse cluster connection for distributed operations

## Error Handling

Each processor implements comprehensive error handling:

- Graceful degradation when optional features are unavailable
- Detailed error messages with context information
- Retry mechanisms for transient failures
- Fallback options for critical operations

## Testing

The modular architecture enables comprehensive testing:

- Unit tests for individual processors
- Integration tests for storage service orchestration
- Mock-based testing for different storage modes
- Performance testing for each storage type

## Future Enhancements

The modular architecture enables future enhancements:

- Additional storage backends (e.g., S3, PostgreSQL)
- Advanced caching mechanisms
- Automated data lifecycle management
- Enhanced monitoring and metrics