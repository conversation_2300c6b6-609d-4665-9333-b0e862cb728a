"""
KPI Results Cleanup DAG

This DAG implements automated cleanup logic for KPI result tables including:
- Identifying and marking jobs that have exceeded retention periods
- Finding and marking jobs superseded by newer results from reruns  
- Dropping result tables for expired/outdated jobs
- Updating metadata table lifecycle status accordingly
- Safety checks to prevent accidental deletion of active results

Author: KPIClick System
Created: 2025-01-01
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.exceptions import AirflowException

# Import multi-storage cleanup utilities
import sys
import os
# Add project root to path for imports (adjust path as needed for your Airflow setup)
sys.path.append('/opt/airflow/dags')
try:
    from src.utils.multi_storage_cleanup import MultiStorageCleanupManager
except ImportError:
    MultiStorageCleanupManager = None
    logging.warning("MultiStorageCleanupManager not available, using legacy cleanup method")

# DAG Configuration
DEFAULT_ARGS = {
    'owner': 'kpiclick-system',
    'depends_on_past': False,
    'start_date': days_ago(1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'max_active_runs': 1,  # Prevent concurrent cleanup runs
}

# DAG Configuration Variables (can be set in Airflow Variables)
CLEANUP_CONFIG = {
    'dry_run': Variable.get('kpi_cleanup_dry_run', default_var=True, deserialize_json=False),
    'max_tables_per_run': int(Variable.get('kpi_cleanup_max_tables', default_var=50)),
    'safety_check_enabled': Variable.get('kpi_cleanup_safety_check', default_var=True, deserialize_json=False),
    'retention_buffer_days': int(Variable.get('kpi_cleanup_retention_buffer', default_var=7)),
    'stuck_job_timeout_hours': int(Variable.get('kpi_cleanup_stuck_job_timeout_hours', default_var=3)),
    'clickhouse_conn_id': Variable.get('kpi_clickhouse_conn_id', default_var='clickhouse_default'),
    'notification_email': Variable.get('kpi_cleanup_notification_email', default_var='<EMAIL>'),
}

def get_clickhouse_connection():
    """Get ClickHouse connection using Airflow connection management."""
    try:
        # Use PostgresHook as a base for ClickHouse connection
        # In production, you might want to use a dedicated ClickHouse hook
        hook = PostgresHook(postgres_conn_id=CLEANUP_CONFIG['clickhouse_conn_id'])
        return hook.get_conn()
    except Exception as e:
        logging.error(f"Failed to establish ClickHouse connection: {e}")
        raise AirflowException(f"Database connection failed: {e}")

def identify_expired_jobs(**context) -> List[Dict[str, Any]]:
    """
    Identify jobs that have exceeded their retention period or are stuck in progress.

    This includes:
    - Jobs with status='done' that have exceeded retention_days + buffer_days
    - Jobs with status='in_progress' that have been running for more than stuck_job_timeout_hours

    Returns:
        List of job records that should be marked as EXPIRED
    """
    logging.info("Starting identification of expired jobs")

    try:
        conn = get_clickhouse_connection()
        cursor = conn.cursor()

        buffer_days = CLEANUP_CONFIG['retention_buffer_days']
        stuck_timeout_hours = CLEANUP_CONFIG['stuck_job_timeout_hours']
        max_tables = CLEANUP_CONFIG['max_tables_per_run']

        # Query to find jobs that have exceeded retention period OR are stuck in progress
        query = f"""
        SELECT
            id,
            job_id,
            final_result_table,
            created_at,
            retention_days,
            lifecycle_status,
            analysis_name,
            username,
            status,
            CASE
                WHEN status = 'done' AND created_at < (now() - toIntervalDay(retention_days + {buffer_days})) THEN 'retention_period_exceeded'
                WHEN status = 'in_progress' AND created_at < (now() - toIntervalHour({stuck_timeout_hours})) THEN 'stuck_in_progress'
                ELSE 'unknown'
            END as expiry_reason
        FROM metadata.results_metadata
        WHERE lifecycle_status = 'ACTIVE'
          AND (
            (status = 'done' AND created_at < (now() - toIntervalDay(retention_days + {buffer_days})))
            OR
            (status = 'in_progress' AND created_at < (now() - toIntervalHour({stuck_timeout_hours})))
          )
          AND final_result_table != ''
        ORDER BY created_at
        LIMIT {max_tables}
        """

        cursor.execute(query)
        expired_jobs = cursor.fetchall()

        # Count different types of expired jobs
        retention_expired = sum(1 for job in expired_jobs if job[9] == 'retention_period_exceeded')
        stuck_jobs = sum(1 for job in expired_jobs if job[9] == 'stuck_in_progress')

        logging.info(f"Found {len(expired_jobs)} expired jobs: {retention_expired} retention expired, {stuck_jobs} stuck in progress")

        # Convert to list of dictionaries for easier handling
        expired_jobs_list = []
        for job in expired_jobs:
            expired_jobs_list.append({
                'id': job[0],
                'job_id': job[1],
                'final_result_table': job[2],
                'created_at': job[3],
                'retention_days': job[4],
                'lifecycle_status': job[5],
                'analysis_name': job[6],
                'username': job[7],
                'status': job[8],
                'expiry_reason': job[9]
            })

        cursor.close()
        conn.close()

        # Store results in XCom for next task
        context['task_instance'].xcom_push(key='expired_jobs', value=expired_jobs_list)

        return expired_jobs_list

    except Exception as e:
        logging.error(f"Failed to identify expired jobs: {e}")
        raise AirflowException(f"Expired job identification failed: {e}")

def identify_superseded_jobs(**context) -> List[Dict[str, Any]]:
    """
    Identify jobs that have been superseded by newer results from reruns.
    
    Returns:
        List of job records that should be marked as OUTDATED
    """
    logging.info("Starting identification of superseded jobs")
    
    try:
        conn = get_clickhouse_connection()
        cursor = conn.cursor()
        
        # Query to find jobs superseded by newer ones with same analysis parameters
        query = f"""
        WITH ranked_jobs AS (
            SELECT
                id,
                job_id,
                final_result_table,
                analysis_name,
                kpi_type,
                id_panel,
                username,
                created_at,
                lifecycle_status,
                periods,
                ROW_NUMBER() OVER (
                    PARTITION BY analysis_name, kpi_type, id_panel, username, periods
                    ORDER BY created_at DESC
                ) as rn
            FROM metadata.results_metadata
            WHERE lifecycle_status = 'ACTIVE'
              AND status = 'done'
              AND final_result_table != ''
        ),
        superseded_with_superseding AS (
            SELECT
                s.id as superseded_id,
                s.job_id as superseded_job_id,
                s.final_result_table,
                s.analysis_name,
                s.created_at as superseded_created_at,
                s.lifecycle_status,
                n.id as superseding_id,
                n.job_id as superseding_job_id,
                n.created_at as superseding_created_at
            FROM ranked_jobs s
            INNER JOIN ranked_jobs n ON (
                s.analysis_name = n.analysis_name
                AND s.kpi_type = n.kpi_type
                AND s.id_panel = n.id_panel
                AND s.username = n.username
                AND s.periods = n.periods
                AND n.rn = 1  -- The newest job (superseding)
            )
            WHERE s.rn > 1  -- Older jobs (superseded)
        )
        SELECT
            superseded_id,
            superseded_job_id,
            final_result_table,
            analysis_name,
            superseded_created_at,
            lifecycle_status,
            superseding_id,
            superseding_job_id,
            superseding_created_at
        FROM superseded_with_superseding
        ORDER BY analysis_name, superseded_created_at
        LIMIT {CLEANUP_CONFIG['max_tables_per_run']}
        """

        cursor.execute(query)
        superseded_jobs = cursor.fetchall()

        logging.info(f"Found {len(superseded_jobs)} jobs that have been superseded")

        # Convert to list of dictionaries
        superseded_jobs_list = []
        for job in superseded_jobs:
            superseded_jobs_list.append({
                'id': job[0],
                'job_id': job[1],
                'final_result_table': job[2],
                'analysis_name': job[3],
                'created_at': job[4],
                'lifecycle_status': job[5],
                'superseding_id': job[6],
                'superseding_job_id': job[7],
                'superseding_created_at': job[8]
            })
        
        cursor.close()
        conn.close()
        
        # Store results in XCom for next task
        context['task_instance'].xcom_push(key='superseded_jobs', value=superseded_jobs_list)
        
        return superseded_jobs_list
        
    except Exception as e:
        logging.error(f"Failed to identify superseded jobs: {e}")
        raise AirflowException(f"Superseded job identification failed: {e}")

def perform_safety_checks(**context) -> bool:
    """
    Perform safety checks before proceeding with cleanup.
    
    Returns:
        True if safe to proceed, False otherwise
    """
    logging.info("Performing safety checks")
    
    if not CLEANUP_CONFIG['safety_check_enabled']:
        logging.warning("Safety checks are disabled - proceeding without validation")
        return True
    
    try:
        expired_jobs = context['task_instance'].xcom_pull(key='expired_jobs', task_ids='identify_expired_jobs')
        superseded_jobs = context['task_instance'].xcom_pull(key='superseded_jobs', task_ids='identify_superseded_jobs')
        
        total_jobs_to_cleanup = len(expired_jobs or []) + len(superseded_jobs or [])
        
        # Safety check 1: Don't delete too many tables at once
        if total_jobs_to_cleanup > CLEANUP_CONFIG['max_tables_per_run']:
            logging.error(f"Safety check failed: Too many tables to cleanup ({total_jobs_to_cleanup} > {CLEANUP_CONFIG['max_tables_per_run']})")
            return False
        
        # Safety check 2: Verify we're not deleting all active jobs
        conn = get_clickhouse_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM metadata.results_metadata WHERE lifecycle_status = 'ACTIVE'")
        active_jobs_count = cursor.fetchone()[0]
        
        if total_jobs_to_cleanup >= active_jobs_count * 0.5:  # Don't delete more than 50% of active jobs
            logging.error(f"Safety check failed: Attempting to delete too high percentage of active jobs ({total_jobs_to_cleanup}/{active_jobs_count})")
            cursor.close()
            conn.close()
            return False
        
        cursor.close()
        conn.close()
        
        logging.info(f"Safety checks passed: {total_jobs_to_cleanup} jobs to cleanup, {active_jobs_count} active jobs total")
        return True

    except Exception as e:
        logging.error(f"Safety check failed with error: {e}")
        return False

def update_metadata_status(**context) -> Dict[str, int]:
    """
    Update metadata table to mark jobs as EXPIRED or OUTDATED.

    Returns:
        Dictionary with counts of updated records
    """
    logging.info("Starting metadata status updates")

    try:
        expired_jobs = context['task_instance'].xcom_pull(key='expired_jobs', task_ids='identify_expired_jobs')
        superseded_jobs = context['task_instance'].xcom_pull(key='superseded_jobs', task_ids='identify_superseded_jobs')

        conn = get_clickhouse_connection()
        cursor = conn.cursor()

        updated_counts = {'expired': 0, 'superseded': 0}

        # Update expired jobs
        if expired_jobs:
            for job in expired_jobs:
                cleanup_reason = job.get('expiry_reason', 'retention_period_exceeded')
                if not CLEANUP_CONFIG['dry_run']:
                    update_query = f"""
                    ALTER TABLE metadata.results_metadata
                    UPDATE
                        lifecycle_status = 'EXPIRED',
                        marked_for_deletion_at = now(),
                        cleanup_reason = '{cleanup_reason}'
                    WHERE id = '{job['id']}'
                    """
                    cursor.execute(update_query)
                    updated_counts['expired'] += 1
                    logging.info(f"Marked job {job['id']} as EXPIRED (reason: {cleanup_reason})")
                else:
                    logging.info(f"DRY RUN: Would mark job {job['id']} as EXPIRED (reason: {cleanup_reason})")
                    updated_counts['expired'] += 1

        # Update superseded jobs
        if superseded_jobs:
            for job in superseded_jobs:
                superseding_job_id = job.get('superseding_id', '')
                if not CLEANUP_CONFIG['dry_run']:
                    update_query = f"""
                    ALTER TABLE metadata.results_metadata
                    UPDATE
                        lifecycle_status = 'OUTDATED',
                        marked_for_deletion_at = now(),
                        cleanup_reason = 'superseded_by_newer_result',
                        superseded_by = '{superseding_job_id}'
                    WHERE id = '{job['id']}'
                    """
                    cursor.execute(update_query)
                    updated_counts['superseded'] += 1
                    logging.info(f"Marked job {job['job_id']} as OUTDATED (superseded by {job.get('superseding_job_id', 'unknown')})")
                else:
                    logging.info(f"DRY RUN: Would mark job {job['id']} as OUTDATED (superseded by {job.get('superseding_job_id', 'unknown')})")
                    updated_counts['superseded'] += 1

        cursor.close()
        conn.close()

        logging.info(f"Updated metadata status: {updated_counts['expired']} expired, {updated_counts['superseded']} superseded")

        # Store results for next task
        context['task_instance'].xcom_push(key='update_counts', value=updated_counts)

        return updated_counts

    except Exception as e:
        logging.error(f"Failed to update metadata status: {e}")
        raise AirflowException(f"Metadata status update failed: {e}")

def drop_result_tables(**context) -> Dict[str, Any]:
    """
    Drop result tables for jobs marked as EXPIRED or OUTDATED.

    Enhanced version that supports multi-storage cleanup for both
    standard and distributed tables using appropriate connections.

    Returns:
        Dictionary with cleanup results and statistics
    """
    logging.info("Starting result table cleanup with multi-storage support")

    # Initialize multi-storage cleanup manager if available
    multi_storage_manager = None
    if MultiStorageCleanupManager:
        try:
            multi_storage_manager = MultiStorageCleanupManager(dry_run=CLEANUP_CONFIG['dry_run'])
            if multi_storage_manager.connect():
                logging.info("Multi-storage cleanup manager initialized successfully")
            else:
                logging.warning("Failed to initialize multi-storage manager, falling back to legacy method")
                multi_storage_manager = None
        except Exception as e:
            logging.warning(f"Multi-storage manager initialization failed: {e}, using legacy method")
            multi_storage_manager = None

    try:
        conn = get_clickhouse_connection()
        cursor = conn.cursor()

        # Get tables to drop (jobs marked for deletion but not yet deleted)
        query = f"""
        SELECT
            id,
            job_id,
            final_result_table,
            lifecycle_status,
            marked_for_deletion_at
        FROM metadata.results_metadata
        WHERE lifecycle_status IN ('EXPIRED', 'OUTDATED')
          AND deleted_at IS NULL
          AND final_result_table != ''
          AND marked_for_deletion_at IS NOT NULL
        ORDER BY marked_for_deletion_at
        LIMIT {CLEANUP_CONFIG['max_tables_per_run']}
        """

        cursor.execute(query)
        tables_to_drop = cursor.fetchall()

        cleanup_results = {
            'tables_dropped': 0,
            'tables_failed': 0,
            'tables_not_found': 0,
            'dropped_table_names': [],
            'failed_table_names': [],
            'storage_type_breakdown': {'cluster': 0, 'storage': 0},
            'multi_storage_used': multi_storage_manager is not None
        }

        # Use multi-storage cleanup if available, otherwise fall back to legacy method
        if multi_storage_manager and tables_to_drop:
            # Extract table names for batch processing
            table_names = [record[2] for record in tables_to_drop]  # final_result_table is index 2

            # Perform batch cleanup using multi-storage manager
            batch_results = multi_storage_manager.cleanup_table_batch(table_names)

            # Update cleanup results with batch results
            cleanup_results['tables_dropped'] = batch_results['successful_drops']
            cleanup_results['tables_failed'] = batch_results['failed_drops']
            cleanup_results['tables_not_found'] = batch_results['tables_not_found']
            cleanup_results['dropped_table_names'] = batch_results['dropped_table_names']
            cleanup_results['failed_table_names'] = batch_results['failed_table_names']
            cleanup_results['storage_type_breakdown'] = batch_results['storage_type_breakdown']

            # Update metadata for successfully processed tables
            if not CLEANUP_CONFIG['dry_run']:
                for table_record in tables_to_drop:
                    table_id, job_id, table_name, status, marked_at = table_record

                    # Check if table was successfully processed (dropped or not found)
                    if (table_name in batch_results['dropped_table_names'] or
                        table_name not in batch_results['failed_table_names']):

                        try:
                            update_query = f"""
                            ALTER TABLE metadata.results_metadata
                            UPDATE
                                lifecycle_status = 'DELETED',
                                deleted_at = now()
                            WHERE id = '{table_id}'
                            """
                            cursor.execute(update_query)
                            logging.debug(f"Updated metadata for table {table_name}")
                        except Exception as update_error:
                            logging.error(f"Failed to update metadata for table {table_name}: {update_error}")

        else:
            # Legacy cleanup method for individual table processing
            logging.info("Using legacy cleanup method")

            for table_record in tables_to_drop:
                table_id, job_id, table_name, status, marked_at = table_record

                try:
                    # Check if table exists before attempting to drop
                    check_query = f"EXISTS TABLE {table_name}"
                    cursor.execute(check_query)
                    table_exists = cursor.fetchone()[0]

                    if table_exists:
                        if not CLEANUP_CONFIG['dry_run']:
                            # Drop the table using standard syntax
                            drop_query = f"DROP TABLE IF EXISTS {table_name}"
                            cursor.execute(drop_query)

                            # Update metadata to mark as deleted
                            update_query = f"""
                            ALTER TABLE metadata.results_metadata
                            UPDATE
                                lifecycle_status = 'DELETED',
                                deleted_at = now()
                            WHERE id = '{table_id}'
                            """
                            cursor.execute(update_query)

                            cleanup_results['tables_dropped'] += 1
                            cleanup_results['dropped_table_names'].append(table_name)
                            logging.info(f"Dropped table: {table_name} (job_id: {job_id})")
                        else:
                            logging.info(f"DRY RUN: Would drop table {table_name} (job_id: {job_id})")
                            cleanup_results['tables_dropped'] += 1
                            cleanup_results['dropped_table_names'].append(table_name)
                    else:
                        # Table doesn't exist, mark as deleted anyway
                        if not CLEANUP_CONFIG['dry_run']:
                            update_query = f"""
                            ALTER TABLE metadata.results_metadata
                            UPDATE
                                lifecycle_status = 'DELETED',
                                deleted_at = now(),
                                cleanup_reason = cleanup_reason || ' (table_not_found)'
                            WHERE id = '{table_id}'
                            """
                            cursor.execute(update_query)

                        cleanup_results['tables_not_found'] += 1
                        logging.warning(f"Table {table_name} not found, marked as deleted anyway")

                except Exception as table_error:
                    cleanup_results['tables_failed'] += 1
                    cleanup_results['failed_table_names'].append(table_name)
                    logging.error(f"Failed to drop table {table_name}: {table_error}")

        cursor.close()
        conn.close()

        # Clean up multi-storage manager connections
        if multi_storage_manager:
            multi_storage_manager.disconnect()

        # Enhanced logging with storage type breakdown
        log_msg = (f"Table cleanup completed: {cleanup_results['tables_dropped']} dropped, "
                  f"{cleanup_results['tables_failed']} failed, {cleanup_results['tables_not_found']} not found")

        if cleanup_results.get('storage_type_breakdown'):
            breakdown = cleanup_results['storage_type_breakdown']
            log_msg += f" (Storage: {breakdown['storage']}, Cluster: {breakdown['cluster']})"

        logging.info(log_msg)

        return cleanup_results

    except Exception as e:
        # Ensure cleanup of multi-storage manager even on error
        if multi_storage_manager:
            try:
                multi_storage_manager.disconnect()
            except:
                pass
        logging.error(f"Failed to drop result tables: {e}")
        raise AirflowException(f"Table cleanup failed: {e}")

def generate_cleanup_report(**context) -> str:
    """
    Generate a summary report of the cleanup operation.

    Returns:
        Formatted cleanup report string
    """
    logging.info("Generating cleanup report")

    try:
        # Get data from previous tasks
        expired_jobs = context['task_instance'].xcom_pull(key='expired_jobs', task_ids='identify_expired_jobs') or []
        superseded_jobs = context['task_instance'].xcom_pull(key='superseded_jobs', task_ids='identify_superseded_jobs') or []
        update_counts = context['task_instance'].xcom_pull(key='update_counts', task_ids='update_metadata_status') or {}
        cleanup_results = context['task_instance'].xcom_pull(key='return_value', task_ids='drop_result_tables') or {}

        # Generate report
        report = f"""
KPI Results Cleanup Report
==========================
Execution Date: {context['ds']}
Dry Run Mode: {CLEANUP_CONFIG['dry_run']}

Jobs Identified for Cleanup:
- Expired (retention period exceeded): {len(expired_jobs)}
- Superseded (newer results available): {len(superseded_jobs)}
- Total jobs identified: {len(expired_jobs) + len(superseded_jobs)}

Metadata Updates:
- Jobs marked as EXPIRED: {update_counts.get('expired', 0)}
- Jobs marked as OUTDATED: {update_counts.get('superseded', 0)}

Table Cleanup Results:
- Tables successfully dropped: {cleanup_results.get('tables_dropped', 0)}
- Tables failed to drop: {cleanup_results.get('tables_failed', 0)}
- Tables not found: {cleanup_results.get('tables_not_found', 0)}

Dropped Tables:
{chr(10).join(f"- {table}" for table in cleanup_results.get('dropped_table_names', []))}

Failed Tables:
{chr(10).join(f"- {table}" for table in cleanup_results.get('failed_table_names', []))}

Configuration Used:
- Max tables per run: {CLEANUP_CONFIG['max_tables_per_run']}
- Retention buffer days: {CLEANUP_CONFIG['retention_buffer_days']}
- Stuck job timeout hours: {CLEANUP_CONFIG['stuck_job_timeout_hours']}
- Safety checks enabled: {CLEANUP_CONFIG['safety_check_enabled']}
"""

        logging.info("Cleanup report generated successfully")
        logging.info(report)

        return report

    except Exception as e:
        logging.error(f"Failed to generate cleanup report: {e}")
        return f"Error generating report: {e}"

# DAG Definition
dag = DAG(
    'kpi_results_cleanup',
    default_args=DEFAULT_ARGS,
    description='Automated cleanup of KPI result tables and metadata',
    schedule_interval='@daily',  # Run daily at midnight
    catchup=False,  # Don't run for past dates
    max_active_runs=1,  # Prevent concurrent runs
    tags=['kpi', 'cleanup', 'maintenance'],
    doc_md=__doc__,
)

# Task 1: Identify expired jobs
identify_expired_task = PythonOperator(
    task_id='identify_expired_jobs',
    python_callable=identify_expired_jobs,
    dag=dag,
    doc_md="""
    Identifies jobs that have exceeded their retention period or are stuck in progress.

    This task queries the metadata table to find jobs where:
    - lifecycle_status = 'ACTIVE' AND status = 'done' AND created_at < (now() - retention_days - buffer_days)
    - OR lifecycle_status = 'ACTIVE' AND status = 'in_progress' AND created_at < (now() - stuck_timeout_hours)

    Stuck jobs are those that have been in 'in_progress' status for longer than the configured
    timeout period (default: 3 hours), indicating they may have failed to update their status.

    Results are stored in XCom for downstream tasks.
    """,
)

# Task 2: Identify superseded jobs
identify_superseded_task = PythonOperator(
    task_id='identify_superseded_jobs',
    python_callable=identify_superseded_jobs,
    dag=dag,
    doc_md="""
    Identifies jobs that have been superseded by newer results from reruns.

    This task finds jobs with identical analysis parameters (analysis_name, kpi_type,
    id_panel, username, periods) where newer jobs exist, marking older ones as candidates
    for OUTDATED status.

    Results are stored in XCom for downstream tasks.
    """,
)

# Task 3: Perform safety checks
safety_check_task = PythonOperator(
    task_id='perform_safety_checks',
    python_callable=perform_safety_checks,
    dag=dag,
    doc_md="""
    Performs safety checks before proceeding with cleanup operations.

    Safety checks include:
    - Verifying total cleanup count doesn't exceed max_tables_per_run
    - Ensuring we're not deleting more than 50% of active jobs
    - Validating database connectivity

    If safety checks fail, the DAG will stop execution.
    """,
)

# Task 4: Update metadata status
update_metadata_task = PythonOperator(
    task_id='update_metadata_status',
    python_callable=update_metadata_status,
    dag=dag,
    doc_md="""
    Updates the metadata table to mark identified jobs with appropriate lifecycle status.

    Updates include:
    - Setting lifecycle_status to 'EXPIRED' for retention-exceeded jobs
    - Setting lifecycle_status to 'OUTDATED' for superseded jobs
    - Setting marked_for_deletion_at timestamp
    - Recording cleanup_reason

    Respects dry_run configuration.
    """,
)

# Task 5: Drop result tables
drop_tables_task = PythonOperator(
    task_id='drop_result_tables',
    python_callable=drop_result_tables,
    dag=dag,
    doc_md="""
    Drops result tables for jobs marked as EXPIRED or OUTDATED.

    This task:
    - Queries for jobs marked for deletion but not yet deleted
    - Checks table existence before attempting drops
    - Drops tables and updates metadata with deleted_at timestamp
    - Handles errors gracefully and logs failures

    Respects dry_run configuration.
    """,
)

# Task 6: Generate cleanup report
generate_report_task = PythonOperator(
    task_id='generate_cleanup_report',
    python_callable=generate_cleanup_report,
    dag=dag,
    doc_md="""
    Generates a comprehensive report of the cleanup operation.

    The report includes:
    - Summary of jobs identified and processed
    - Metadata update statistics
    - Table cleanup results
    - Configuration used
    - List of dropped and failed tables

    Report is logged and can be used for monitoring and auditing.
    """,
)

# Task Dependencies
# Run identification tasks in parallel
[identify_expired_task, identify_superseded_task] >> safety_check_task

# Sequential execution after safety checks
safety_check_task >> update_metadata_task >> drop_tables_task >> generate_report_task

# Optional: Add email notification task for failures
# You can uncomment and configure this if email notifications are needed
"""
from airflow.operators.email import EmailOperator

email_notification_task = EmailOperator(
    task_id='send_failure_notification',
    to=[CLEANUP_CONFIG['notification_email']],
    subject='KPI Cleanup DAG Failed',
    html_content='''
    <h3>KPI Results Cleanup DAG Failed</h3>
    <p>The KPI results cleanup process has failed. Please check the logs for details.</p>
    <p>Execution Date: {{ ds }}</p>
    <p>DAG: {{ dag.dag_id }}</p>
    <p>Task: {{ task.task_id }}</p>
    ''',
    dag=dag,
    trigger_rule='one_failed',  # Send email if any upstream task fails
)

# Add email task to the end of the pipeline
generate_report_task >> email_notification_task
"""
