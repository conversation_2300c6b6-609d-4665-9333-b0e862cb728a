{# Template for creating replicated table for final KPI data in cluster mode #}
CREATE TABLE IF NOT EXISTS job_result.{{ table_suffix }}_replicated
(
    {% if labels %}
        {% for axis_key, axis_data in axes.items() %}
            {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                "{{ axis_key }}_{{ axis_data["name"] }}" String,
            {% endif %}
            {% if position_numbers %}
                "{{ axis_data["name"] }}_position_number" UInt16,
            {% endif %}
        {% endfor %}
    {% endif %}
    period_name String,
    Fact String,
    Value Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (
    {% if labels %}
        {% for axis_key, axis_data in axes.items() %}
            {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                "{{ axis_key }}_{{ axis_data["name"] }}",
            {% endif %}
            {% if position_numbers %}
                "{{ axis_data["name"] }}_position_number",
            {% endif %}
        {% endfor %}
    {% endif %}
    period_name
)
SETTINGS index_granularity = 8192
