{# Template for creating replicated table for final KPI data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.final_{{ table_suffix }}_replicated
(
    {% for fact in required_facts %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            {{ fact }} Float64,
        {% elif fact == "population" %}
            {{ fact }} Float64,
        {% elif fact == "volume_loyalty_base" %}
            volume_loyalty_base Float64,
        {% elif fact == "value_loyalty_base" %}
            value_loyalty_base Float64,
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
        buyers_raw Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "equalto", "trips_raw")|list %}
        trips_raw Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["trips_000", "frequency", "spend_per_trip", "packs_per_trip", "volume_per_trip"])|list %}
        trips Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["buyers_000", "penetration", "frequency", "spend_per_buyer", "volume_per_buyer", "repeat_rate"])|list %}
        buyers Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["repeaters_000", "repeat_rate", "penetration_repeaters", "trial_000"])|list %}
        trial Float64,
        repeaters Float64,
    {% endif %}
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number String,
        {% endif %}
    {% endfor %}
    projectc Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    projectc
)
SETTINGS index_granularity = 8192
