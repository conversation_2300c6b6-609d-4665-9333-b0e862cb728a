import logging
import time
import uuid
import copy
from typing import Optional, <PERSON>, Tuple, Dict, Any, Union

from src.core.clickhouse_connect import ClickHouseConnection
from src.core.clickhouse_driver import (
    ClickHouseConnection as ClickHouseDriverConnection,
)
from src.queries.query_builder import ClickHouseQuery
from src.services.storage_service import StorageService
from src.models.kpi import JobParameters, KPIType
from src.utils.job_api_client import set_job_progress_message
from src.models.axis import Period
from src.core.exceptions import QueryError
from src.utils.data_processing import calculate_ddl_chunk_indices


class QueryProcessor:
    """Class to handle query processing for KPI analysis."""

    def __init__(
        self,
        connection: ClickHouseConnection,
        query_builder: ClickHouseQuery,
        storage_service: Optional[StorageService] = None,
        progress_tracker=None,
        cluster_connection: Optional[ClickHouseDriverConnection] = None,
    ):
        """
        Initialize QueryProcessor.

        Args:
            connection: ClickHouse database connection (for storage)
            query_builder: Query builder instance
            storage_service: Optional StorageService instance. If not provided, will create one automatically
            progress_tracker: Optional progress tracker for real-time updates
            cluster_connection: Optional cluster connection for distributed processing

        Raises:
            ValueError: If any required parameter is None
        """
        # Validate required parameters
        if not connection:
            raise ValueError("ClickHouse connection is required")
        if not query_builder:
            raise ValueError("Query builder is required")

        # Store dependencies
        self.connection = connection  # Storage connection (clickhouse-connect)
        self.cluster_connection = (
            cluster_connection  # Cluster connection (clickhouse-driver)
        )
        self.query_builder = query_builder
        self.progress_tracker = progress_tracker

        # Determine if we're in cluster mode
        self.cluster_mode = (
            cluster_connection is not None
            and hasattr(query_builder, "cluster_mode")
            and query_builder.cluster_mode
        )

        # Create storage service with appropriate configuration
        if storage_service:
            self.storage_service = storage_service
        else:
            # Create storage service with cluster configuration if available
            # Determine storage mode based on query builder configuration
            cluster_storage_mode = (
                hasattr(query_builder, "cluster_config")
                and query_builder.cluster_config
                and query_builder.cluster_config.get("use_cluster_storage", False)
            )

            self.storage_service = StorageService(
                connection=connection,
                cluster_connection=cluster_connection,
                cluster_mode=self.cluster_mode,
                cluster_storage_mode=cluster_storage_mode,
            )

        # Initialize logger and state
        self.logger = logging.getLogger(self.__class__.__name__)
        self.temp_tables: List[str] = []

        # Log storage configuration
        storage_info = self.storage_service.get_storage_mode_info()
        self.logger.info(f"Storage configuration: {storage_info['description']}")
        self.logger.debug(f"Available processors: {storage_info['processors']}")

    def _create_temp_table(
        self, table_name: str, query: str, query_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a temporary table and track it.

        Args:
            table_name: Name of the temporary table to create
            query: SQL query to populate the table
            query_id: Optional custom query ID for tracking

        Returns:
            Table name if successful, None if failed
        """
        self.logger.info(f"Creating temporary table '{table_name}'")

        # Report progress if tracker is available
        if self.progress_tracker:
            self.progress_tracker.update_query_stage(
                table_name, f"Creating {table_name} table"
            )

        settings = {"query_id": query_id} if query_id else None

        try:
            result = self.connection.create_temp_table(
                query, table_name, "Memory", None, settings
            )

            # Check if the result is a tuple indicating an error
            if isinstance(result, tuple) and len(result) == 2 and result[0] == "ERROR":
                error_msg = f"Error creating temp table {table_name}: {result[1]}"
                self.logger.error(error_msg)
                return None

            # Add to tracking list
            self.temp_tables.append(table_name)

            self.logger.info(f"Temporary table '{table_name}' created successfully")
            return table_name

        except Exception as e:
            self.logger.error(f"Exception creating temp table {table_name}: {e}")
            return None

    def _drop_temp_table(self, table_name: str) -> bool:
        """
        Drop a specific temporary table.

        Args:
            table_name: Name of the temporary table to drop

        Returns:
            True if successful, False otherwise
        """
        if table_name not in self.temp_tables:
            self.logger.warning(
                f"Temporary table '{table_name}' not found in tracking list"
            )
            return False

        try:
            self.logger.info(f"Dropping temporary table '{table_name}'")

            # Handle cluster mode tables differently
            if (
                self.cluster_mode
                and self.cluster_connection
                and (
                    "_distributed" in table_name.lower()
                    or "_replicated" in table_name.lower()
                )
            ):
                result = self.cluster_connection.drop_temp_table(table_name)
            else:
                result = self.connection.drop_temp_table(table_name)

            if result:
                self.temp_tables.remove(table_name)
                self.logger.info(f"Temporary table '{table_name}' dropped successfully")
                return True
            else:
                self.logger.warning(f"Failed to drop temporary table '{table_name}'")
                return False

        except QueryError as e:
            # Log the error but continue
            self.logger.warning(
                f"ClickHouse error dropping temporary table '{table_name}': {e}"
            )
            if hasattr(e, "error_code"):
                self.logger.warning(
                    f"Error code: {e.error_code}, type: {getattr(e, 'error_type', 'unknown')}"
                )
            return False
        except Exception as e:
            self.logger.warning(f"Failed to drop temporary table '{table_name}': {e}")
            return False

        except Exception as e:
            self.logger.error(f"Error dropping cluster table '{table_name}': {e}")
            return False

    def _generate_chunk_queries(
        self,
        period: Period,
        kpi_type: KPIType,
        query_id_prefix: str,
    ) -> Tuple[List[str], List[str], List[Tuple[int, int]]]:
        """
        Generate SQL templates for each chunk and return final queries and query IDs.

        Args:
            period: Period model containing chunking configuration
            kpi_type: Type of KPI to process
            query_id_prefix: Prefix for generating query IDs

        Returns:
            Tuple of (final_queries, final_chunk_query_ids, chunk_indices)
        """
        fourth_axis_data = self.query_builder.axes["fourth_axis"]
        ddl_queries = fourth_axis_data.ddl["queries"]
        total_ddls = len(ddl_queries)

        # Get chunking parameters
        total_positions_product = getattr(period, "total_positions", None)
        threshold = getattr(period, "threshold", None)
        fourth_axis_positions = fourth_axis_data.axis_positions

        # Validate parameters before proceeding
        if total_positions_product is None:
            raise ValueError("total_positions not found in period model")
        if threshold is None:
            raise ValueError("threshold not found in period model")
        if fourth_axis_positions is None or (
            isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) == 0
        ):
            raise ValueError("fourth_axis.axis_positions is None or empty")

        # Extract the actual axis positions value (it's a list, we need the first element)
        if isinstance(fourth_axis_positions, int):
            axis_positions_value = fourth_axis_positions
        elif isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) > 0:
            axis_positions_value = fourth_axis_positions[0]
        else:
            raise ValueError("fourth_axis.axis_positions has invalid type or value")

        if axis_positions_value == 0:
            raise ValueError("fourth_axis.axis_positions value is 0")

        # Calculate chunk indices using the utility function
        chunk_indices = calculate_ddl_chunk_indices(
            total_positions=total_positions_product,
            axis_positions=axis_positions_value,
            threshold=threshold,
            total_ddls=total_ddls,
        )

        # Log chunking information
        other_axes_positions_product = total_positions_product // axis_positions_value
        max_fourth_axis_positions_per_chunk = threshold // other_axes_positions_product
        max_fourth_axis_positions_per_chunk = max(
            1, max_fourth_axis_positions_per_chunk
        )

        self.logger.info(f"Total positions (from period): {total_positions_product}")
        self.logger.info(f"Fourth axis positions: {fourth_axis_positions}")
        self.logger.info(
            f"Other axes positions product: {other_axes_positions_product}"
        )
        self.logger.info(
            f"Max fourth_axis positions per chunk: {max_fourth_axis_positions_per_chunk}"
        )
        self.logger.info(
            f"Splitting {total_ddls} DDL queries into {len(chunk_indices)} chunks"
        )

        # Store original fourth_axis data
        original_fourth_axis = copy.deepcopy(fourth_axis_data)

        final_queries = []
        final_chunk_query_ids = []

        # Generate queries for each chunk
        for chunk_idx, (start_idx, end_idx) in enumerate(chunk_indices):
            chunk_ddls = ddl_queries[start_idx:end_idx]

            # Calculate estimated total positions for this chunk
            estimated_fourth_axis_positions = len(chunk_ddls)
            estimated_total_positions = (
                estimated_fourth_axis_positions * other_axes_positions_product
            )

            self.logger.info(
                f"Generating query for chunk {chunk_idx + 1}/{len(chunk_indices)}: DDLs {start_idx} to {end_idx - 1}"
            )
            self.logger.info(
                f"Estimated positions in chunk: {estimated_fourth_axis_positions} (fourth_axis) * {other_axes_positions_product} (others) = {estimated_total_positions} total"
            )

            # Create a deep copy of the original fourth_axis AxisData
            fourth_axis_copy = copy.deepcopy(original_fourth_axis)
            fourth_axis_copy.ddl["queries"] = chunk_ddls

            # Temporarily set the sliced copy in query_builder
            self.query_builder.axes["fourth_axis"] = fourth_axis_copy

            # Generate final_query_chunk using the sliced fourth_axis
            final_query_chunk = self.query_builder.query_final(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )

            # Generate custom query ID for this chunk's final query
            final_chunk_query_id = (
                f"{query_id_prefix}final_chunk_{chunk_idx}_{uuid.uuid4().hex[:8]}"
            )

            final_queries.append(final_query_chunk)
            final_chunk_query_ids.append(final_chunk_query_id)

        # Restore the original fourth_axis AxisData
        self.query_builder.axes["fourth_axis"] = original_fourth_axis

        return final_queries, final_chunk_query_ids, chunk_indices

    def _process_chunks(
        self,
        final_queries: List[str],
        final_chunk_query_ids: List[str],
    ) -> Tuple[List[str], Optional[Dict[str, Any]]]:
        """
        Execute the chunked query processing workflow.

        Args:
            final_queries: List of final SQL queries for each chunk
            final_chunk_query_ids: List of query IDs for final queries

        Raises:
            QueryError: If any query fails

        Returns:
            Tuple of (List of query ids and Error info if failed or None)
        """
        num_chunks = len(final_queries)
        query_ids = []

        # Process each chunk
        try:
            for chunk_idx, (final_query_chunk, final_chunk_query_id) in enumerate(
                zip(final_queries, final_chunk_query_ids)
            ):
                # For first chunk: create final result table, for subsequent chunks: insert directly
                if chunk_idx == 0:
                    # First chunk: Create final table for this chunk's final data
                    self.logger.info(f"Processing chunk {chunk_idx + 1}/{num_chunks}")
                    chunk_table = self._create_temp_table(
                        "final",
                        final_query_chunk,
                        final_chunk_query_id,
                    )
                    query_ids.append(final_chunk_query_id)
                    if not chunk_table:
                        error_msg = "Failed to create final table, cannot continue"
                        self.logger.error(error_msg)
                        raise QueryError(error_msg)

                else:
                    # Subsequent chunks: Insert directly into existing final result table
                    self.logger.info(f"Processing chunk {chunk_idx + 1}/{num_chunks}")
                    insert_settings = {"query_id": final_chunk_query_id}
                    insert_query = self.connection.add_rows_from_query(
                        "final", final_query_chunk, insert_settings
                    )
                    query_ids.append(final_chunk_query_id)
                    if insert_query:
                        self.logger.info(
                            f"Chunk {chunk_idx + 1}/{num_chunks} processed successfully"
                        )
                    else:
                        error_msg = f"Failed to insert data from chunk {chunk_idx + 1}/{num_chunks} into final table from query {final_query_chunk}, cannot continue"
                        self.logger.error(error_msg)
                        raise QueryError(error_msg)

                    self.logger.info(
                        f"Chunk {chunk_idx + 1}/{num_chunks} inserted into final table"
                    )

            self.logger.info(f"All {num_chunks} chunks processed successfully")
            return query_ids, None
        except Exception as e:
            # Log the error
            error_message = f"Failed to process chunks: {e}"
            self.logger.error(error_message)
            return query_ids, {"error": error_message}

    def validate_storage_configuration(self) -> bool:
        """
        Validate storage configuration and connections.

        Returns:
            True if storage is properly configured, False otherwise
        """
        try:
            # Test connections
            connection_tests = self.storage_service.test_connections()

            if not connection_tests.get("main_connection", False):
                self.logger.error("Main storage connection test failed")
                return False

            # Check cluster connection if cluster mode is enabled
            if self.cluster_mode:
                cluster_test = connection_tests.get("cluster_connection")
                if cluster_test is False:
                    self.logger.error("Cluster connection test failed")
                    return False
                elif cluster_test is None:
                    self.logger.warning(
                        "Cluster mode enabled but no cluster connection available"
                    )

            # Get processor status
            processor_status = self.storage_service.get_processor_status()
            self.logger.debug(f"Processor status: {processor_status}")

            return True

        except Exception as e:
            self.logger.error(f"Storage validation failed: {str(e)}")
            return False

    def get_storage_statistics(
        self, table_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get storage statistics and information.

        Args:
            table_name: Optional table name to get specific table info

        Returns:
            Dictionary with storage statistics
        """
        try:
            stats = {
                "storage_mode": self.storage_service.get_storage_mode_info(),
                "processor_status": self.storage_service.get_processor_status(),
                "connection_tests": self.storage_service.test_connections(),
            }

            if table_name:
                stats["table_info"] = self.storage_service.get_table_info(table_name)

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get storage statistics: {str(e)}")
            return {"error": str(e)}

    def _cleanup_temp_tables(self, temp_tables: Optional[List[str]] = None) -> None:
        """
        Clean up temporary tables.

        Args:
            temp_tables: List of temporary table names to drop. If None, all tables in self.temp_tables will be dropped.
            cluster_mode: If True, handles cleanup for cluster distributed and replicated tables
        """
        # Determine which tables to drop
        tables_to_drop = temp_tables if temp_tables is not None else self.temp_tables[:]

        # Skip if no tables to drop
        if not tables_to_drop:
            self.logger.debug("No temporary tables to clean up")
            return

        self.logger.info(f"Cleaning up {len(tables_to_drop)} temporary tables")

        # Make a copy of the list to avoid modifying it while iterating
        tables_to_drop_copy = tables_to_drop[:]

        # Drop each table
        for table in tables_to_drop_copy:
            if table in self.temp_tables:
                self._drop_temp_table(table)

        # Log remaining tables
        if self.temp_tables:
            self.logger.debug(
                f"Remaining temporary tables: {', '.join(self.temp_tables)}"
            )

    def process_query(
        self,
        job_parameters: JobParameters,
        queries: Dict[str, Union[List[str], List[List[str]]]],
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Process a KPI query and store the results directly in ClickHouse.

        Args:
            job_parameters: JobParameters model containing all validated data
            queries: Dictionary mapping query names to either [query_string, query_id] or [query_strings, query_ids] for chunked queries

        Returns:
            Tuple of (Temporary table name if successful or None, Error info if failed or None)
        """
        # Start tracking total job execution time
        job_start_time = time.time()

        # Validate storage configuration before processing
        if not self.validate_storage_configuration():
            error_msg = "Storage configuration validation failed"
            self.logger.error(error_msg)
            return None, {"error": error_msg}

        try:
            if not self.connection.execute_command(queries["max_query_size"][0]):
                error_msg = "Failed to set max_query_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.connection.execute_command(queries["max_ast_elements"][0]):
                error_msg = "Failed to set max_ast_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.connection.execute_command(queries["max_expanded_ast_elements"][0]):
                error_msg = "Failed to set max_ast_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.connection.execute_command(
                queries["optimize_aggregation_in_order"][0]
            ):
                error_msg = (
                    "Failed to set optimize_aggregation_in_order, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)
            
            if not self.connection.execute_command(
                queries["query_plan_max_optimizations_to_apply"][0]
            ):
                error_msg = "Failed to set query_plan_max_optimizations_to_apply, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 1. Create 'pre_axis' temporary table
            pre_axis_table = self._create_temp_table(
                "pre_axis", queries["pre_axis"][0], queries["pre_axis"][1]
            )
            job_parameters.extend(query_ids=queries["pre_axis"][1])
            if not pre_axis_table:
                error_msg = "Failed to create pre_axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 2. Create 'axis' temporary table (which depends on 'pre_axis')
            axis_table = self._create_temp_table(
                "axis", queries["axis"][0], queries["axis"][1]
            )
            job_parameters.extend(query_ids=queries["axis"][1])
            if not axis_table:
                error_msg = "Failed to create axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 3. Drop 'pre_axis' temporary table after 'axis' is created but before 'buyers_final' is created
            self._drop_temp_table("pre_axis")

            # 4. Create 'buyers_final' temporary table (which depends on 'axis')
            buyers_table = self._create_temp_table(
                "buyers_final", queries["buyers"][0], queries["buyers"][1]
            )
            job_parameters.extend(query_ids=queries["buyers"][1])
            if not buyers_table:
                error_msg = "Failed to create buyers_final table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 5. Drop axis table as it's no longer needed
            self._drop_temp_table("axis")

            # 6. Create 'Buyers_Uplift_Factor' (BUF) table if needed
            buf_table = None
            if "BUF" in self.query_builder.required_facts:
                buf_table = self._create_temp_table(
                    "Buyers_Uplift_Factor", queries["buf"][0], queries["buf"][1]
                )
                job_parameters.extend(query_ids=queries["buf"][1])
                if not buf_table:
                    error_msg = (
                        "Failed to create Buyers_Uplift_Factor table, cannot continue"
                    )
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

            # 7. Create final temporary table (which depends on 'buyers_final' and 'Buyers_Uplift_Factor' if applicable)
            if job_parameters.period.split_axis == "fourth_axis":
                self.logger.info(
                    f"Processing {job_parameters.period.split_axis} with chunking, threshold: {job_parameters.period.threshold}"
                )
                chunk_query_ids, error_info = self._process_chunks(
                    queries["final"][0], queries["final"][1]
                )
                job_parameters.extend(query_ids=chunk_query_ids)
            else:
                final_table = self._create_temp_table(
                    "final", queries["final"][0], queries["final"][1]
                )
                job_parameters.extend(query_ids=queries["final"][1])
                if not final_table:
                    error_msg = "Failed to create final table, cannot continue"
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

            # 8. Drop BUF table if it was created and buyers_final
            if buf_table:
                self._drop_temp_table("Buyers_Uplift_Factor")
            self._drop_temp_table("buyers_final")

            # 9. Create result table while all required tables are still available
            result_table = self._create_temp_table(
                f"{job_parameters.combined_result_id}",
                queries["result_table"][0],
                queries["result_table"][1],
            )
            job_parameters.extend(query_ids=queries["result_table"][1])
            if not result_table:
                error_msg = (
                    "Failed to create temporary table for result, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 10. Drop final table
            self._drop_temp_table("final")

            # Transfer to permanent storage using storage service
            self.logger.info(
                f"Transferring final result from {result_table} to permanent storage"
            )

            transfer_result = self.storage_service.transfer_temp_table_to_storage(
                temp_table_name=result_table,
                result_id=job_parameters.combined_result_id,
                period=job_parameters.period,
                query_prefix="_".join(job_parameters.combined_result_id.split("_")[:2])
                + "_",
            )

            # Update total rows in job parameters (if not already set) and query IDs
            rows_transferred = (
                transfer_result["rows_transferred"] if transfer_result["success"] else 0
            )  # Handle transfer failure
            job_parameters.result_rows += rows_transferred or 0
            job_parameters.extend(
                query_ids=[transfer_result["transfer_query_id"]]
                if transfer_result["transfer_query_id"]
                else []
            )

            # Drop all remaining temporary tables
            self._cleanup_temp_tables()

            if not transfer_result["success"]:
                error_msg = f"Failed to transfer temporary table to storage: {transfer_result['error']}"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            self.logger.info(
                f"Transferred {transfer_result['rows_transferred']} rows to permanent storage"
            )

            # Calculate job duration
            job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Result completed in {job_duration_ms:.2f} ms")

            return transfer_result["permanent_table"], None

        except Exception as e:
            # Calculate job duration for error case
            job_duration_ms = (time.time() - job_start_time) * 1000

            # Extract error code if available
            error_code = getattr(e, "error_code", None)
            error_message = str(e)

            if not error_code:  # If not directly available, try regex
                # Try to extract error code from the message using different patterns
                import re  # Import locally to avoid unused global import warning if not needed

                # Pattern 1: "error code 62"
                match = re.search(r"error code (\d+)", error_message.lower())
                if match:
                    error_code = int(match.group(1))
                else:
                    # Pattern 2: "Code: 62."
                    match = re.search(r"Code: (\d+)[.\s]", error_message)
                    if match:
                        error_code = int(match.group(1))

            # Log the extracted error code
            if error_code:
                self.logger.info(f"Extracted ClickHouse error code: {error_code}")

            # Log storage information for debugging
            try:
                storage_info = self.storage_service.get_storage_mode_info()
                self.logger.error(
                    f"Storage mode during error: {storage_info['storage_location']}"
                )
                self.logger.error(f"Available processors: {storage_info['processors']}")
            except Exception as storage_error:
                self.logger.warning(
                    f"Could not get storage info during error: {storage_error}"
                )

            # Create error info for storage
            error_info = {
                "message": error_message,
                "error_code": error_code,
                "period": job_parameters.period.label,
                "job_duration_ms": job_duration_ms,
            }

            self.logger.error(
                f"Result processing failed for period {job_parameters.period.label}: {error_message}"
            )

            return None, error_info

        finally:
            # Calculate total job duration
            total_job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Total job duration: {total_job_duration_ms:.2f} ms")

            # Clean up any remaining temporary tables that weren't explicitly dropped
            if self.temp_tables:
                self.logger.info(
                    f"Cleaning up {len(self.temp_tables)} remaining temporary tables"
                )
                self._cleanup_temp_tables(self.temp_tables)

    def _create_cluster_distributed_table(
        self,
        table_name: str,
        table_query: str,
        query: str,
        order_by: str = "tuple()",
        sharding_key: Optional[str] = "rand()",
        table_query_id: Optional[str] = None,
        query_id: Optional[str] = None,
        cluster_database: Optional[str] = None,
        table_list: Optional[bool] = True,
    ) -> Optional[str]:
        """
        Create a distributed table on cluster and track it.

        Args:
            table_name: Name of the distributed table to create
            table_query: Query for replicated table creation
            query: SQL query to populate the table
            order_by: Order by clause,
            sharding_key: Sharding keys for distributed table,
            table_query_id: Optional custom query ID for tracking replicated table creation
            query_id: Optional custom query ID for tracking
            cluster_database: Optional name of db
            table_list: Optional parameter for adding created table to table list

        Returns:
            Table name if successful, None if failed
        """
        if not self.cluster_mode or not self.cluster_connection:
            self.logger.error(
                "Cluster mode not enabled or cluster connection not available"
            )
            return None

        self.logger.info(f"Creating distributed table '{table_name}' on cluster")

        # Report progress if tracker is available
        if self.progress_tracker:
            self.progress_tracker.update_query_stage(
                table_name, f"Creating {table_name} distributed table"
            )

        settings_replicated = {"query_id": table_query_id} if table_query_id else None
        settings = {"query_id": query_id} if query_id else None

        try:
            self.logger.debug(
                f"Executing cluster DDL query for replicated table creation: {query[:200]}..."
            )

            replicated_table = self.cluster_connection.create_replicated_table(
                table_query=table_query,
                table_name=table_name,
                order_by=order_by,
                template=True,
                settings=settings_replicated,
            )

            if (
                isinstance(replicated_table, tuple)
                and len(replicated_table) == 2
                and replicated_table[0] == "ERROR"
            ):
                error_msg = f"Error creating replicated table {table_name}: {replicated_table[1]}"
                self.logger.error(error_msg)
                return None

            # Add to tracking list if replicated_table is a string
            if isinstance(replicated_table, str) and table_list:
                self.temp_tables.append(replicated_table)
            else:
                self.logger.warning(
                    f"Unexpected replicated_table type: {type(replicated_table)}"
                )

            self.logger.debug(
                f"Executing cluster DDL query for distributed table creation: {query[:200]}..."
            )

            distributed_table = self.cluster_connection.create_distributed_table(
                replicated_table,
                table_name,
                sharding_key=sharding_key,
                cluster_database=cluster_database,
            )

            if (
                isinstance(distributed_table, tuple)
                and len(distributed_table) == 2
                and distributed_table[0] == "ERROR"
            ):
                error_msg = f"Error creating distributed table {table_name}: {distributed_table[1]}"
                self.logger.error(error_msg)
                return None

            # Add to tracking list if distributed_table is a string
            if isinstance(distributed_table, str) and table_list:
                self.temp_tables.append(distributed_table)
            else:
                self.logger.warning(
                    f"Unexpected distributed_table type: {type(distributed_table)}"
                )

            self.logger.debug(
                "Executing cluster DDL query for intserting data to replicated table..."
            )
            self.logger.debug(
                f"Insert query: {query[:200]}..."
            )

            result = self.cluster_connection.add_rows_from_query(
                target_table=distributed_table, source_query=query, settings=settings
            )

            if not result:
                error_msg = f"Error creating distributed table {table_name} - no result returned"
                self.logger.error(error_msg)
                return None

            self.logger.info(f"Distributed table '{table_name}' created successfully")
            return table_name

        except Exception as e:
            error_msg = f"Failed to create distributed table {table_name}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return None

    def process_cluster_query(
        self,
        job_parameters: JobParameters,
        queries: Dict[str, Union[List[str], List[List[str]]]],
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Process a KPI query using cluster-based distributed tables.

        Args:
            job_parameters: JobParameters model containing all validated data
            queries: Dictionary mapping query names to either [query_string, query_id] or [query_strings, query_ids] for chunked queries

        Returns:
            Tuple of (Result table name if successful or None, Error info if failed or None)
        """
        if not self.cluster_mode or not self.cluster_connection:
            error_msg = "Cluster mode not enabled or cluster connection not available"
            self.logger.error(error_msg)
            return None, {"error": error_msg}

        # Start tracking total job execution time
        job_start_time = time.time()

        # Validate storage configuration before processing
        if not self.validate_storage_configuration():
            error_msg = "Storage configuration validation failed"
            self.logger.error(error_msg)
            return None, {"error": error_msg}

        try:
            self.logger.info("Starting cluster-based query processing")

            # Validate cluster configuration
            cluster_config = self.query_builder.cluster_config or {}
            if not cluster_config.get("cluster_name"):
                error_msg = "Cluster name not configured"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            if not cluster_config.get("database"):
                error_msg = "Cluster database not configured"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            # Generate unique table suffix for this job
            if not cluster_config.get("table_suffix"):
                error_msg = "Cluster table suffix not configured"
                self.logger.error(error_msg)
                return None, {"error": error_msg}
            else:
                table_suffix = cluster_config.get("table_suffix")

            self.logger.info(
                f"Using cluster: {cluster_config.get('cluster_name')}, "
                f"database: {cluster_config.get('database')}, "
                f"table suffix: {table_suffix}"
            )

            # Update cluster config in query builder
            self.query_builder.cluster_config.update({"table_suffix": table_suffix})

            if not self.cluster_connection.execute_command(
                queries["max_query_size"][0]
            ):
                error_msg = "Failed to set max_query_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.cluster_connection.execute_command(
                queries["max_ast_elements"][0]
            ):
                error_msg = "Failed to set max_ast_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.cluster_connection.execute_command(
                queries["optimize_aggregation_in_order"][0]
            ):
                error_msg = (
                    "Failed to set optimize_aggregation_in_order, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.cluster_connection.execute_command(
                queries["distributed_aggregation_memory_efficient"][0]
            ):
                error_msg = "Failed to set distributed_aggregation_memory_efficient, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)
            else:
                self.logger.info(
                    "Parameter distributed_aggregation_memory_efficient was set"
                )

            if not self.cluster_connection.execute_command(
                queries["query_plan_max_optimizations_to_apply"][0]
            ):
                error_msg = "Failed to set query_plan_max_optimizations_to_apply, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)
            else:
                self.logger.info(
                    "Parameter query_plan_max_optimizations_to_apply was set"
                )

            # 1. Create replicated and distributed tables for pre-axis
            self.logger.info("Creating pre-axis table on cluster")

            job_parameters.extend(query_ids=queries["pre_axis"][1])
            if not self._create_cluster_distributed_table(
                table_name=f"pre_axis_{table_suffix}",
                table_query=queries["pre_axis_replicated"][0],
                query=queries["pre_axis"][0],
                table_query_id=queries["pre_axis_replicated"][1],
                query_id=queries["pre_axis"][1],
                sharding_key="mod(hhkey, 2)",
            ):
                error_msg = "Failed to create pre-axis table"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            # 2. Create replicated and distributed tables for axis
            self.logger.info("Creating axis table on cluster")

            job_parameters.extend(query_ids=queries["axis"][1])
            if not self._create_cluster_distributed_table(
                table_name=f"axis_{table_suffix}",
                table_query=queries["axis_replicated"][0],
                query=queries["axis"][0],
                table_query_id=queries["axis_replicated"][1],
                query_id=queries["axis"][1],
                sharding_key="mod(hhkey, 2)",
            ):
                error_msg = "Failed to create axis table"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            # 3. Create replicated and distributed tables for buyers
            self.logger.info("Creating buyers table on cluster")

            job_parameters.extend(query_ids=queries["buyers"][1])
            if not self._create_cluster_distributed_table(
                table_name=f"buyers_final_{table_suffix}",
                table_query=queries["buyers_replicated"][0],
                query=queries["buyers"][0],
                table_query_id=queries["buyers_replicated"][1],
                query_id=queries["buyers"][1],
                sharding_key="mod(hhkey, 2)",
            ):
                error_msg = "Failed to create buyers replicated table"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            # 4. Create replicated and distributed tables for BUF
            self.logger.info("Creating Buyers_Uplift_Factor table on cluster")

            if "BUF" in self.query_builder.required_facts:
                job_parameters.extend(query_ids=queries["buf"][1])
                if not self._create_cluster_distributed_table(
                    table_name=f"buf_{table_suffix}",
                    table_query=queries["buf_replicated"][0],
                    query=queries["buf"][0],
                    table_query_id=queries["buf_replicated"][1],
                    query_id=queries["buf"][1],
                    sharding_key="cityHash64(rwbasis)",
                ):
                    error_msg = "Failed to create BUF table"
                    self.logger.error(error_msg)
                    return None, {"error": error_msg}

            # 6. Create replicated and distributed tables for final
            self.logger.info("Creating final table on cluster")

            job_parameters.extend(query_ids=queries["final"][1])
            if not self._create_cluster_distributed_table(
                table_name=f"final_{table_suffix}",
                table_query=queries["final_replicated"][0],
                query=queries["final"][0],
                table_query_id=queries["final_replicated"][1],
                query_id=queries["final"][1],
            ):
                error_msg = "Failed to create final table"
                self.logger.error(error_msg)
                return None, {"error": error_msg}

            # 7. Create result table directly in storage using direct creation mode
            self.logger.info("Creating result table directly in storage")

            transfer_result = (
                self.storage_service.transfer_temp_table_to_storage_enhanced(
                    result_id=job_parameters.combined_result_id,
                    period=job_parameters.period,
                    query_prefix="_".join(
                        job_parameters.combined_result_id.split("_")[:2]
                    )
                    + "_",
                    result_table_query=queries["result_table"][0],
                    result_table_structure_query=queries["result_table_replicated"][0],
                    direct_creation=True,
                )
            )

            # Drop all remaining temporary tables
            self._cleanup_temp_tables()

            # Update total rows in job parameters (if not already set) and query IDs
            rows_transferred = (
                transfer_result["rows_transferred"] if transfer_result["success"] else 0
            )  # Handle transfer failure

            # Add query IDs from direct creation operation
            if transfer_result.get("transfer_query_id"):
                job_parameters.extend(query_ids=[transfer_result["transfer_query_id"]])

            # Add result_table query IDs that were used in direct creation
            if queries.get("result_table") and len(queries["result_table"]) > 1:
                job_parameters.extend(query_ids=[queries["result_table"][1]])
            if (
                queries.get("result_table_replicated")
                and len(queries["result_table_replicated"]) > 1
            ):
                job_parameters.extend(query_ids=[queries["result_table_replicated"][1]])

            job_parameters.result_rows += rows_transferred or 0

            if not transfer_result["success"]:
                error_msg = f"Failed to transfer temporary table to storage: {transfer_result['error']}"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            self.logger.info(
                f"Transferred {transfer_result['rows_transferred']} rows to permanent storage"
            )

            # Calculate job duration
            job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Result completed in {job_duration_ms:.2f} ms")
            self.logger.info("Cluster-based query processing completed successfully")

            return transfer_result["permanent_table"], None

        except Exception as e:
            error_msg = f"Cluster query processing failed: {e}"
            self.logger.error(error_msg)
            return None, {"error": error_msg}

        finally:
            # Clean up temporary tables
            self._cleanup_temp_tables()

            # Calculate total job duration
            total_job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(
                f"Total cluster job duration: {total_job_duration_ms:.2f} ms"
            )
