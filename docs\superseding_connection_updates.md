# Superseding Logic Connection and ID Updates

## Overview

Updated the superseding logic to use the correct identifiers and database connections as specified:

1. Use `id` (result_id) for filling the `superseded_by` column
2. Use cluster connection for working with result tables in `job_result` database
3. Use normal connection for `job_results` database

## Changes Made

### 1. **Manual Cleanup Script** (`scripts/manual_cleanup.py`)

**Updated superseding logic to use result_id:**
```python
# Before
superseding_job_id = job.get('superseding_job_id', '')
if self.update_job_status(job['id'], 'OUTDATED', 'superseded_by_newer_result', superseding_job_id):

# After  
superseding_result_id = job.get('superseding_id', '')  # Use result_id for superseded_by
superseding_job_id = job.get('superseding_job_id', '')  # For logging only
if self.update_job_status(job['id'], 'OUTDATED', 'superseded_by_newer_result', superseding_result_id):
```

**Enhanced logging to show both job_id and result_id:**
```python
logger.info(f"Job {job['job_id']} superseded by job {superseding_job_id} (result_id: {superseding_result_id})")
```

### 2. **Airflow DAG** (`airflow/dags/kpi_results_cleanup_dag.py`)

**Updated superseding logic to use result_id:**
```python
# Before
superseding_job_id = job.get('superseding_job_id', '')
superseded_by = '{superseding_job_id}'

# After
superseding_result_id = job.get('superseding_id', '')  # Use result_id for superseded_by
superseding_job_id = job.get('superseding_job_id', '')  # For logging only
superseded_by = '{superseding_result_id}'
```

**Enhanced logging:**
```python
logging.info(f"Marked job {job['job_id']} as OUTDATED (superseded by job {superseding_job_id}, result_id: {superseding_result_id})")
```

### 3. **Multi-Storage Cleanup Manager** (`src/utils/multi_storage_cleanup.py`)

**Updated connection logic for database-specific connections:**
```python
# Before
if database == 'kpi_result' and table.endswith('_distributed'):
    return 'cluster', self.cluster_connection

# After
if database == 'job_result':  # All tables in job_result use cluster connection
    return 'cluster', self.cluster_connection
```

**Connection Rules:**
- **job_result database**: Uses cluster connection
- **job_results database**: Uses normal/storage connection
- **All other databases**: Uses normal/storage connection

## Database Connection Logic

### **Connection Selection Rules:**

1. **job_result database** (with underscore):
   - Uses cluster connection
   - Handles distributed and replicated tables
   - Example: `job_result.data_job123_abc456`

2. **job_results database** (without underscore):
   - Uses normal/storage connection
   - Handles standard local tables
   - Example: `job_results.data_job123_def789`

3. **Other databases**:
   - Uses normal/storage connection
   - Example: `kpi_results.data_job123_ghi012`

### **Implementation Details:**

The `MultiStorageCleanupManager.determine_storage_type()` method now:
- Parses the full table name to extract database name
- Applies connection rules based on database name
- Returns appropriate connection type and connection object
- Provides fallback to storage connection if cluster connection unavailable

## Superseded_by Field Logic

### **Field Population:**

The `superseded_by` column now consistently uses `result_id` (the `id` field) across all components:

1. **Real-time superseding** (MetadataStorageProcessor):
   - Uses `new_job_result_id` for `superseded_by`
   - Already correctly implemented

2. **Manual cleanup** (scripts/manual_cleanup.py):
   - Uses `superseding_id` (result_id) for `superseded_by`
   - Updated from using `superseding_job_id`

3. **Airflow DAG** (airflow/dags/kpi_results_cleanup_dag.py):
   - Uses `superseding_id` (result_id) for `superseded_by`
   - Updated from using `superseding_job_id`

### **Data Structure Mapping:**

The query results provide both identifiers:
- `superseding_id`: The result_id of the superseding job (used for `superseded_by`)
- `superseding_job_id`: The job_id of the superseding job (used for logging)

## Benefits

### 1. **Consistent Identifier Usage**
- All superseding mechanisms now use `result_id` for the `superseded_by` field
- Provides unique identification of superseding relationships
- Enables precise tracking of which specific result superseded another

### 2. **Proper Connection Management**
- Database-specific connection usage ensures optimal performance
- Cluster connections for distributed operations in `job_result`
- Standard connections for local operations in `job_results`
- Automatic fallback handling for connection failures

### 3. **Enhanced Logging**
- Shows both job_id (user-friendly) and result_id (system-unique)
- Better debugging and audit trail capabilities
- Clear indication of superseding relationships

## Verification

### **Database Queries:**

```sql
-- Check superseding relationships with result_id
SELECT 
    id,
    job_id,
    superseded_by,
    lifecycle_status,
    cleanup_reason
FROM metadata.results_metadata 
WHERE superseded_by != ''
ORDER BY job_id, created_at;

-- Verify result_id format in superseded_by
SELECT 
    COUNT(*) as total_superseded,
    COUNT(CASE WHEN LENGTH(superseded_by) > 20 THEN 1 END) as result_id_format,
    COUNT(CASE WHEN LENGTH(superseded_by) <= 20 THEN 1 END) as job_id_format
FROM metadata.results_metadata 
WHERE superseded_by != '';
```

### **Connection Testing:**

```python
# Test connection selection
manager = MultiStorageCleanupManager()
manager.connect()

# job_result database should use cluster connection
storage_type, conn = manager.determine_storage_type('job_result.data_123')
assert storage_type == 'cluster'

# job_results database should use storage connection  
storage_type, conn = manager.determine_storage_type('job_results.data_456')
assert storage_type == 'storage'
```

## Conclusion

The updates ensure that:
1. The `superseded_by` field consistently contains `result_id` values for precise tracking
2. Database connections are used appropriately based on database names
3. All superseding mechanisms (real-time, manual, Airflow) behave consistently
4. Enhanced logging provides better visibility into superseding operations