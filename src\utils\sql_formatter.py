"""
SQL formatting utilities using SQLFluff.

This module provides utilities for formatting SQL queries using SQLFluff.
"""

import logging
import sqlfluff
from typing import Optional, Dict, Any, Tuple

# Set up logging
logger = logging.getLogger(__name__)


def format_sql(sql: str, dialect: str = "clickhouse") -> Tuple[str, bool]:
    """
    Format a SQL query using SQLFluff.

    Args:
        sql: SQL query to format
        dialect: SQL dialect to use for formatting

    Returns:
        Tuple of (formatted SQL, success flag)
    """
    if not sql:
        return "", True

    try:
        # Use a simpler approach with direct fix
        try:
            # Format the SQL query
            formatted = sqlfluff.fix(sql, dialect=dialect)
            return formatted, True
        except Exception as inner_e:
            logger.warning(f"SQLFluff fix failed: {inner_e}")

            # Try a more basic approach
            lines = sql.split("\n")
            formatted_lines = []

            # Add newlines after common SQL clauses
            current_line = ""
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Check if line starts with a SQL clause
                starts_with_clause = False
                for clause in [
                    "SELECT",
                    "FROM",
                    "WHERE",
                    "GROUP BY",
                    "HAVING",
                    "ORDER BY",
                    "LIMIT",
                ]:
                    if line.upper().startswith(clause):
                        if current_line:
                            formatted_lines.append(current_line)
                        current_line = line
                        starts_with_clause = True
                        break

                if not starts_with_clause:
                    current_line += " " + line

            if current_line:
                formatted_lines.append(current_line)

            # Format the result
            result = "\n".join(formatted_lines)
            return result, True

    except Exception as e:
        logger.error(f"Error formatting SQL query: {e}")
        # Return the original SQL if formatting fails
        return sql, False


def format_jinja_template(
    template: str, dialect: str = "clickhouse"
) -> Tuple[str, bool]:
    """
    Format a Jinja SQL template using SQLFluff.

    Args:
        template: Jinja SQL template to format
        dialect: SQL dialect to use for formatting

    Returns:
        Tuple of (formatted template, success flag)
    """
    if not template:
        return "", True

    try:
        # For Jinja templates, we'll use a simpler approach
        # First, check if the template has Jinja syntax
        has_jinja = "{{" in template or "{%" in template

        if has_jinja:
            # For templates with Jinja syntax, we'll just do basic indentation
            # and not use SQLFluff since it might struggle with complex Jinja templates
            lines = template.split("\n")
            formatted_lines = []
            indent_level = 0

            for line in lines:
                # Adjust indent level based on Jinja blocks
                stripped = line.strip()

                # Decrease indent for end blocks before adding the line
                if stripped.startswith("{% end") or stripped.startswith("{%- end"):
                    indent_level = max(0, indent_level - 1)

                # Add the line with proper indentation
                if stripped:
                    formatted_lines.append("    " * indent_level + stripped)
                else:
                    formatted_lines.append("")

                # Increase indent for block starts after adding the line
                if (
                    stripped.startswith("{% if")
                    or stripped.startswith("{%- if")
                    or stripped.startswith("{% for")
                    or stripped.startswith("{%- for")
                    or stripped.startswith("{% block")
                    or stripped.startswith("{%- block")
                ):
                    indent_level += 1

            return "\n".join(formatted_lines), True
        else:
            # If no Jinja syntax, use regular SQL formatting
            return format_sql(template, dialect=dialect)

    except Exception as e:
        logger.error(f"Error formatting Jinja SQL template: {e}")
        # Return the original template if formatting fails
        return template, False


def lint_sql(
    sql: str, dialect: str = "clickhouse", config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Lint a SQL query using SQLFluff.

    Args:
        sql: SQL query to lint
        dialect: SQL dialect to use for linting
        config: Optional SQLFluff configuration

    Returns:
        Dictionary with linting results
    """
    if not sql:
        return {"success": True, "violations": []}

    try:
        # Set default configuration if not provided
        if config is None:
            config = {
                "dialect": dialect,
                "exclude_rules": [
                    "L016",
                    "L031",
                ],  # Exclude some common rules that might be too strict
            }

        # Lint the SQL query
        lint_result = sqlfluff.lint(sql, dialect=dialect, config=config)

        # Process the linting results
        violations = []
        for violation in lint_result:
            violations.append(
                {
                    "line_no": violation.get("line_no"),
                    "line_pos": violation.get("line_pos"),
                    "code": violation.get("code"),
                    "description": violation.get("description"),
                    "name": violation.get("name"),
                }
            )

        return {"success": len(violations) == 0, "violations": violations}

    except Exception as e:
        logger.error(f"Error linting SQL query: {e}")
        return {"success": False, "error": str(e), "violations": []}
