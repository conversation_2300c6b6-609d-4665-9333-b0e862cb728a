# Superseding Logic Implementation Summary

## Overview

This document summarizes the implementation of proper `superseded_by` column population in the KPI cleanup system. The enhancement provides complete audit trails for job superseding relationships and implements both batch and real-time superseding scenarios.

## Implementation Scope

### 1. Enhanced Cleanup Operations (Scenario 1)

**Files Modified:**
- `scripts/manual_cleanup.py`
- `airflow/dags/kpi_results_cleanup_dag.py`

**Key Changes:**

#### Manual Cleanup Script
- **Enhanced `find_superseded_jobs()` method**: Now returns both superseded jobs AND their superseding job information
- **Updated `update_job_status()` method**: Added `superseded_by` parameter for tracking superseding relationships
- **Modified `cleanup_superseded_jobs()` method**: Populates `superseded_by` column with actual superseding job ID

#### Airflow DAG
- **Enhanced superseding query**: Uses CTE to join superseded jobs with their superseding counterparts
- **Updated metadata update logic**: Includes `superseded_by` field in UPDATE statements
- **Improved logging**: Shows which job superseded which for better audit trails

### 2. Real-time Superseding (Scenario 2)

**Files Modified:**
- `src/processors/storage/metadata_storage_processor.py`

**Key Changes:**

#### MetadataStorageProcessor
- **Enhanced `finalize_job_metadata()` method**: Automatically checks for superseding when job completes successfully
- **New `_check_and_mark_superseded_jobs()` method**: Implements real-time superseding logic
- **Immediate superseding**: Marks older jobs as OUTDATED when new job completes

## Technical Implementation Details

### 1. Enhanced Superseding Query

**Before (Old Logic):**
```sql
WITH ranked_jobs AS (
    SELECT id, job_id, ..., 
           ROW_NUMBER() OVER (PARTITION BY ... ORDER BY created_at DESC) as rn
    FROM metadata.results_metadata
    WHERE lifecycle_status = 'ACTIVE' AND status = 'done'
)
SELECT * FROM ranked_jobs WHERE rn > 1
```

**After (New Logic):**
```sql
WITH ranked_jobs AS (
    SELECT id, job_id, ..., periods,
           ROW_NUMBER() OVER (PARTITION BY ... ORDER BY created_at DESC) as rn
    FROM metadata.results_metadata
    WHERE lifecycle_status = 'ACTIVE' AND status = 'done'
),
superseded_with_superseding AS (
    SELECT 
        s.id as superseded_id,
        s.job_id as superseded_job_id,
        n.id as superseding_id,
        n.job_id as superseding_job_id
    FROM ranked_jobs s
    INNER JOIN ranked_jobs n ON (
        s.analysis_name = n.analysis_name 
        AND s.kpi_type = n.kpi_type 
        AND s.id_panel = n.id_panel 
        AND s.username = n.username 
        AND s.periods = n.periods
        AND n.rn = 1  -- The newest job (superseding)
    )
    WHERE s.rn > 1  -- Older jobs (superseded)
)
SELECT * FROM superseded_with_superseding
```

### 2. Enhanced Update Logic

**Before:**
```sql
ALTER TABLE metadata.results_metadata
UPDATE
    lifecycle_status = 'OUTDATED',
    marked_for_deletion_at = now(),
    cleanup_reason = 'superseded_by_newer_result'
WHERE id = '{job_id}'
```

**After:**
```sql
ALTER TABLE metadata.results_metadata
UPDATE
    lifecycle_status = 'OUTDATED',
    marked_for_deletion_at = now(),
    cleanup_reason = 'superseded_by_newer_result',
    superseded_by = '{superseding_job_id}'
WHERE id = '{job_id}'
```

### 3. Real-time Superseding Logic

**Trigger:** When `finalize_job_metadata()` is called with `status = 'done'`

**Process:**
1. Get analysis parameters of the newly completed job
2. Find existing ACTIVE jobs with identical parameters
3. Mark older jobs as OUTDATED with proper `superseded_by` tracking
4. Log superseding relationships for audit trails

**Query Logic:**
```sql
-- Find older jobs with identical analysis parameters
SELECT id, job_id, analysis_name, created_at
FROM metadata.results_metadata
WHERE analysis_name = '{analysis_name}'
  AND kpi_type = '{kpi_type}'
  AND id_panel = {id_panel}
  AND username = '{username}'
  AND periods = '{periods}'
  AND lifecycle_status = 'ACTIVE'
  AND status = 'done'
  AND final_result_table != ''
  AND id != '{new_job_result_id}'
ORDER BY created_at
```

## Business Logic Rules

### Superseding Criteria
Jobs are superseded when ALL parameters match exactly:
- `analysis_name`: Type of analysis
- `kpi_type`: KPI calculation type  
- `id_panel`: Panel identifier
- `username`: User who initiated analysis
- `periods`: Time periods analyzed

### Additional Requirements
- Jobs must be completed successfully (`status = 'done'`)
- Jobs must be currently active (`lifecycle_status = 'ACTIVE'`)
- Jobs must have valid result tables (`final_result_table != ''`)
- Only older jobs are superseded (based on `created_at`)

### Timing
- **Batch Processing**: During scheduled cleanup runs (Airflow DAG or manual cleanup)
- **Real-time**: Immediately when a new job completes successfully

## Testing

### Test Coverage
Created comprehensive test suite (`tests/test_superseding_logic.py`) covering:

1. **Cleanup Superseding Logic Test**
   - Verifies enhanced `find_superseded_jobs()` returns superseding information
   - Tests `cleanup_superseded_jobs()` populates `superseded_by` column correctly
   - Validates proper lifecycle status transitions

2. **Real-time Superseding Logic Test**
   - Tests automatic superseding during job finalization
   - Verifies immediate marking of older jobs as OUTDATED
   - Validates proper `superseded_by` population

### Test Scenarios
- Multiple jobs with identical analysis parameters
- Jobs with different parameters (should not supersede)
- In-progress jobs (should not be superseded)
- Error jobs (should not supersede others)

## Benefits

### 1. Complete Audit Trail
- Full tracking of which job superseded which
- Enables debugging of superseding relationships
- Supports compliance and audit requirements

### 2. Real-time Processing
- Immediate superseding when jobs complete
- No waiting for cleanup runs
- Faster identification of outdated results

### 3. Enhanced Reliability
- Proper relationship tracking prevents orphaned references
- Improved error handling and logging
- Better visibility into superseding operations

### 4. Backward Compatibility
- Existing cleanup logic continues to work
- Graceful handling of missing superseding information
- No breaking changes to existing APIs

## Usage Examples

### Query Superseding Relationships
```sql
-- Find all jobs superseded by a specific job
SELECT 
    id as superseded_job,
    job_id,
    analysis_name,
    created_at,
    superseded_by
FROM metadata.results_metadata 
WHERE superseded_by = 'specific_job_id';

-- Find superseding chains
SELECT 
    s.job_id as superseded_job,
    n.job_id as superseding_job,
    s.analysis_name,
    s.created_at as superseded_at,
    n.created_at as superseding_at
FROM metadata.results_metadata s
JOIN metadata.results_metadata n ON s.superseded_by = n.id
WHERE s.lifecycle_status = 'OUTDATED'
ORDER BY s.analysis_name, s.created_at;
```

### Manual Cleanup with Superseding
```bash
# List superseded jobs with superseding information
python scripts/manual_cleanup.py --superseded --list-only

# Clean up superseded jobs (populates superseded_by column)
python scripts/manual_cleanup.py --superseded --max-tables 50
```

## Future Enhancements

1. **Superseding Chains**: Track multi-level superseding relationships
2. **Superseding Notifications**: Alert users when their jobs are superseded
3. **Superseding Analytics**: Dashboard showing superseding patterns
4. **Selective Superseding**: Allow users to prevent superseding for specific jobs
5. **Superseding History**: Maintain historical superseding relationships

## Conclusion

The superseding logic implementation provides a robust foundation for tracking job relationships in the KPI cleanup system. It combines batch processing efficiency with real-time responsiveness while maintaining complete audit trails and backward compatibility.
