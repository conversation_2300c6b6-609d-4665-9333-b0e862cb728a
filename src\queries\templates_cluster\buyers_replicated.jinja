{# Template for creating replicated table for buyers data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.buyers_final_{{ table_suffix }}_replicated 
(
    hhkey UInt64,
    rwbasis String,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number String,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact == "weight_wave" %}
            buyers_ww Float64,
        {% endif %}
        {% if fact == "BUF" %}
            trips_raw UInt64,
            trips_ww Float64,
            population Float64,
        {% endif %}
        {% if fact == "rw_compensat" %}
            trips_fullmass Float64,
        {% endif %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            {{ fact }} Float64,
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "trips_raw")|list and  "BUF" not in required_facts %}
        trips_raw UInt64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["loyalty_volume", "loyalty_base_volume"])|list %}
        volume_loyalty_base Float64,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["loyalty_value", "loyalty_base_value"])|list %}
        value_loyalty_base Float64,
    {% endif %}
    projectc Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (hhkey, rwbasis)
SETTINGS index_granularity = 8192
