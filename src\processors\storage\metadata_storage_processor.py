"""
Metadata storage processor for KPI results.

This module handles metadata storage operations including job metadata management,
status tracking, and metadata table operations.
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional

from src.core.clickhouse_connect import ClickHouseConnection
from src.models.axis import Period
from src.models.kpi import JobParameters, KPIType
from src.utils.formatting import NumpyJSONEncoder
from .base_storage_processor import BaseStorageProcessor


class MetadataStorageProcessor(BaseStorageProcessor):
    """
    Processor for metadata storage operations.

    Handles:
    - Job metadata creation and updates
    - Status tracking and management
    - Metadata table operations
    - Job information serialization
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        msg_logger_func=None,
        progress_tracker=None,
    ):
        """
        Initialize the metadata storage processor.

        Args:
            connection: ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
        """
        super().__init__(connection, msg_logger_func, progress_tracker)
        self.logger = logging.getLogger(__name__)

    def initialize_job_metadata(
        self,
        job_parameters: JobParameters,
        username: str,
        result_id: str,
        **kwargs,
    ) -> bool:
        """
        Initialize job metadata with 'in_progress' status.

        Args:
            job_parameters: JobParameters model containing all validated data
            username: Username for the job
            result_id: Combined result ID for the job
            **kwargs: Additional job parameters

        Returns:
            True if successful, False otherwise
        """
        try:
            job_id = job_parameters.job_id
            analysis_name = job_parameters.analysis_name
            periods = job_parameters.periods
            kpi_type = job_parameters.kpi_type
            id_panel = job_parameters.id_panel
            axes = job_parameters.axes
            filters = job_parameters.filters

            self.logger.info(
                f"Initializing job metadata for job {job_id} with result_id {result_id}"
            )

            job_info = {
                "result_id": result_id,
                "job_id": job_id,
                "analysis_name": analysis_name,
                "backend_version": kwargs.get("backend_version", "unknown"),
                "id_panel": id_panel,
                "kpi_type": kpi_type.value,
                "periods": [period.label for period in periods],
                "axes": {
                    key: axis.full_name if axis.full_name is not None else axis.axis_id
                    for key, axis in axes.items()
                },
                "filters": {key: filter.full_name for key, filter in filters.items()},
                "facts": [fact.display_name for fact in job_parameters.facts_axis],
                "su_fact_name": job_parameters.su_fact_data.su_name or ""
                if job_parameters.su_fact_data
                else "",
                "cluster_mode": kwargs.get("cluster_mode", False),
                "cluster_storage_mode": kwargs.get("cluster_storage_mode", False),
                "storage_location": kwargs.get("storage_location", "local"),
                "json_mode": job_parameters.json_mode,
                "created_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
            }

            # Add any additional parameters
            job_info.update(kwargs)

            # Ensure metadata table exists
            self._ensure_results_metadata_table()

            # Insert initial metadata record with 'in_progress' status
            return self._insert_job_metadata(
                username=username,
                result_id=result_id,
                job_id=job_id,
                version=kwargs.get("backend_version", "unknown"),
                analysis_name=analysis_name,
                periods=periods,
                kpi_type=kpi_type,
                id_panel=id_panel,
                job_info=job_info,
                status="in_progress",
            )

        except Exception as e:
            self.logger.error(f"Failed to initialize job metadata: {str(e)}")
            return False

    def update_job_metadata(self, result_id: str, **update_params: Any) -> bool:
        """
        Update job metadata with provided parameters.

        Args:
            result_id: The ID of the result to update
            **update_params: Dictionary of column names and values to update

        Returns:
            True if successful, False otherwise
        """
        try:
            result_id_escaped = self.escape_sql_string(result_id)

            # Process special cases like job_info that needs JSON serialization
            if "job_info" in update_params:
                job_info = update_params["job_info"]
                update_params["job_info"] = json.dumps(job_info, cls=NumpyJSONEncoder)

                # Also update facts column if facts exist in job_info
                if "facts" in job_info and job_info["facts"]:
                    facts_str = json.dumps(job_info["facts"], cls=NumpyJSONEncoder)
                    update_params["facts"] = facts_str

            # Build update expressions
            update_expressions = []
            for column, value in update_params.items():
                if isinstance(value, str):
                    value = self.escape_sql_string(value)
                    update_expressions.append(f"{column} = '{value}'")
                elif isinstance(value, (int, float)):
                    update_expressions.append(f"{column} = {value}")
                else:
                    value = self.escape_sql_string(str(value))
                    update_expressions.append(f"{column} = '{value}'")

            update_query = f"""
                ALTER TABLE metadata.results_metadata
                UPDATE {", ".join(update_expressions)}
                WHERE id = '{result_id_escaped}'
            """

            self.connection.execute_command(update_query)
            self.logger.info(
                f"Updated job metadata for result_id {result_id} with parameters: {list(update_params.keys())}"
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Failed to update job metadata for result_id {result_id}: {str(e)}"
            )
            return False

    def finalize_job_metadata(
        self,
        result_id: str,
        status: str = "done",
        error_info: Optional[Dict[str, Any]] = None,
        result_table_name: Optional[str] = None,
        **kwargs,
    ) -> bool:
        """
        Finalize job metadata by updating status and completion information.
        If status is 'done', automatically check for and mark superseded jobs.

        Args:
            result_id: Combined result ID for the job
            status: Final status ('done' or 'error')
            error_info: Optional error information if status is 'error'
            result_table_name: Optional name of the result table
            **kwargs: Additional parameters to update

        Returns:
            True if successful, False otherwise
        """
        try:
            if not result_id:
                self.logger.error("No result_id provided")
                return False

            completed_at = time.time()

            self.logger.info(
                f"Finalizing job metadata for result_id {result_id} with status {status}"
            )

            # Get current job info
            job_info = self._get_job_info(result_id)
            if not job_info:
                self.logger.error(f"No job info found for result_id {result_id}")
                return False

            # Update job info with completion details
            job_info["completed_at"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(completed_at)
            )
            job_info["status"] = status

            if result_table_name:
                job_info["result_table"] = result_table_name

            if error_info:
                job_info["error_info"] = error_info

            # Add any additional parameters
            # job_info.update(kwargs)

            # Update metadata
            update_params = {
                "status": status,
                "completed_at": completed_at,
                "job_info": job_info,
                # Add additional parameters from kwargs
                **kwargs,
            }

            # Add final_result_table if provided
            if result_table_name:
                update_params["final_result_table"] = result_table_name

            # First, update the current job metadata
            success = self.update_job_metadata(result_id, **update_params)

            # If job completed successfully, check for and mark superseded jobs
            self.logger.info(
                f"Finalization result - Success: {success}, Status: {status}, Result table: {result_table_name}"
            )

            # Check for superseding if job completed successfully and has a result table
            if success and status == "done":
                if result_table_name:
                    self.logger.info(
                        f"Checking for superseded jobs for result_id {result_id}"
                    )
                    self._check_and_mark_superseded_jobs(result_id, job_info["job_id"])
                else:
                    self.logger.warning(
                        f"Job {result_id} completed successfully but no result_table_name provided - skipping superseding check"
                    )

            return success

        except Exception as e:
            self.logger.error(
                f"Failed to finalize job metadata for result_id {result_id}: {str(e)}"
            )
            return False

    def get_job_status(self, result_id: str) -> Optional[str]:
        """
        Get the current status of a job.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job status ('in_progress', 'done', 'error') or None if not found
        """
        try:
            query = f"""
                SELECT status FROM metadata.results_metadata
                WHERE id = '{self.escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            return result.iloc[0]["status"]

        except Exception as e:
            self.logger.error(
                f"Failed to get job status for result_id {result_id}: {str(e)}"
            )
            return None

    def get_job_info(self, result_id: str) -> Optional[Dict[str, Any]]:
        """
        Get job info from metadata.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job info dictionary or None if not found
        """
        return self._get_job_info(result_id)

    def _get_job_info(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get job info from metadata."""
        try:
            query = f"""
                SELECT job_info FROM metadata.results_metadata
                WHERE id = '{self.escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            job_info_str = result.iloc[0]["job_info"]
            return json.loads(job_info_str)

        except Exception as e:
            self.logger.error(
                f"Failed to get job info for result_id {result_id}: {str(e)}"
            )
            return None

    def _check_and_mark_superseded_jobs(self, new_job_result_id: str, job_id: str) -> bool:
        """
        Check if the newly completed job supersedes any existing ACTIVE jobs with the same job_id.
        Mark superseded jobs as OUTDATED with proper superseded_by tracking.

        Args:
            new_job_result_id: The result ID of the newly completed job
            job_id: The job ID of the newly completed job

        Returns:
            True if jobs were marked as superseded, False otherwise
        """
        try:
            new_job_query = f"""
                ALTER TABLE metadata.results_metadata
                UPDATE lifecycle_status = 'OUTDATED',
                marked_for_deletion_at = now (),
                cleanup_reason = 'superseded_by_newer_result',
                superseded_by = '{self.escape_sql_string(new_job_result_id)}'
                WHERE
                id IN (
                    SELECT
                        id
                    FROM metadata.results_metadata
                    WHERE (
                        job_id = '{self.escape_sql_string(job_id)}'
                        AND id != '{self.escape_sql_string(new_job_result_id)}'
                        AND final_result_table != ''
                        AND status = 'done'
                        AND lifecycle_status = 'ACTIVE'
                    )
                )
            """

            try:
                result = self.connection.execute_command(new_job_query)
                if not result:
                    self.logger.debug(f"Failed to mark jobs as superseded for result_id {new_job_result_id}")
                    return False
            except Exception as e:
                self.logger.error(
                    f"Failed to mark jobs as superseded for job_id {job_id} as superseded: {e}"
                )

            return True

        except Exception as e:
            self.logger.error(
                f"Failed to check for superseded jobs for result_id {new_job_result_id}: {e}"
            )
            return False

    def _insert_job_metadata(
        self,
        username: str,
        result_id: str,
        job_id: str,
        version: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
        id_panel: int,
        job_info: Dict[str, Any],
        status: str,
    ) -> bool:
        """Insert job metadata record."""
        try:
            # Serialize job_info to JSON
            job_info_json = json.dumps(job_info, cls=NumpyJSONEncoder)

            # Extract facts from job_info and convert to string
            facts_str = ""
            if "facts" in job_info and job_info["facts"]:
                facts_str = json.dumps(job_info["facts"], cls=NumpyJSONEncoder)

            # Create periods string
            periods_str = ",".join([p.label for p in periods])

            # Escape strings for SQL
            result_id_escaped = self.escape_sql_string(result_id)
            job_id_escaped = self.escape_sql_string(job_id)
            username_escaped = self.escape_sql_string(username)
            analysis_name_escaped = self.escape_sql_string(analysis_name)
            periods_escaped = self.escape_sql_string(periods_str)
            kpi_type_escaped = self.escape_sql_string(kpi_type.value)
            job_info_escaped = self.escape_sql_string(job_info_json)
            status_escaped = self.escape_sql_string(status)
            facts_escaped = self.escape_sql_string(facts_str)

            # Insert metadata record
            insert_query = f"""
                INSERT INTO metadata.results_metadata (
                    id, job_id, analysis_name, periods,
                    kpi_type, id_panel, username, created_at, job_info, backend_version, status, query_ids, facts, lifecycle_status
                )
                VALUES (
                    '{result_id_escaped}', '{job_id_escaped}', '{analysis_name_escaped}',
                    '{periods_escaped}',
                    '{kpi_type_escaped}', {id_panel}, '{username_escaped}', now(), '{job_info_escaped}', '{version}','{status_escaped}', '', '{facts_escaped}', 'ACTIVE'
                )
            """

            self.connection.execute_command(insert_query)
            self.logger.info(f"Inserted job metadata for result_id {result_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to insert job metadata: {str(e)}")
            return False

    def _ensure_results_metadata_table(self):
        """Ensure the results_metadata table exists with the new schema."""
        try:
            # Load and execute the SQL schema file
            import os

            sql_file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                "sql",
                "results_metadata_table.sql",
            )

            if os.path.exists(sql_file_path):
                with open(sql_file_path, "r", encoding="utf-8") as f:
                    sql_content = f.read()

                # Execute the SQL content (may contain multiple statements)
                # Split by semicolon and execute each statement
                statements = [
                    stmt.strip() for stmt in sql_content.split(";") if stmt.strip()
                ]
                for statement in statements:
                    if statement and not statement.startswith("--"):
                        self.connection.execute_command(statement)

                self.logger.debug(
                    "Ensured results_metadata table exists using SQL file"
                )
            else:
                # Fallback to inline SQL if file doesn't exist
                self.logger.warning(
                    f"SQL file not found: {sql_file_path}, using inline SQL"
                )
                self._create_results_metadata_table_inline()

        except Exception as e:
            self.logger.error(f"Failed to ensure results_metadata table: {str(e)}")
            # Try fallback
            try:
                self._create_results_metadata_table_inline()
            except Exception as fallback_error:
                self.logger.error(f"Fallback also failed: {str(fallback_error)}")
                raise e

    def _create_results_metadata_table_inline(self):
        """Create results_metadata table using inline SQL."""
        create_query = """
            CREATE TABLE IF NOT EXISTS metadata.results_metadata (
                id String,
                job_id String,
                analysis_name String,
                periods String,
                kpi_type String,
                id_panel Int32,
                username String,
                created_at DateTime DEFAULT now(),
                completed_at Nullable(Float64),
                job_info String,
                backend_version String,
                status String DEFAULT 'in_progress',
                lifecycle_status String DEFAULT 'ACTIVE',
                query_ids String DEFAULT '',
                facts String DEFAULT '',
                final_result_table String DEFAULT '',
                marked_for_deletion_at Nullable(DateTime),
                deleted_at Nullable(DateTime),
                superseded_by String DEFAULT '',
                cleanup_reason String DEFAULT '',
                retention_days UInt16 DEFAULT 30,
                error_info String DEFAULT '{}'
            )
            ENGINE = MergeTree()
            ORDER BY (id, created_at)
        """

        self.connection.execute_command(create_query)
        self.logger.info("Created results_metadata table using inline SQL")
