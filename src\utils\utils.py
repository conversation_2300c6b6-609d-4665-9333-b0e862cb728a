import toml
import os


def get_poetry_version(project_root="."):
    """
    Retrieves the version from a Poetry pyproject.toml file.

    Args:
        project_root (str): The root directory of the Poetry project.
                            Defaults to the current directory.

    Returns:
        str: The version string if found, otherwise None.
    """
    toml_path = os.path.join(project_root, "pyproject.toml")

    if not os.path.exists(toml_path):
        print(f"Error: pyproject.toml not found at {toml_path}")
        return None

    try:
        with open(toml_path, "r") as f:
            pyproject_data = toml.load(f)

        # Poetry stores its project metadata under [tool.poetry]
        poetry_section = pyproject_data.get("tool", {}).get("poetry", {})
        version = poetry_section.get("version")

        if version:
            return version
        else:
            print("Warning: 'version' not found in [tool.poetry] section.")
            return None

    except toml.TomlDecodeError as e:
        print(f"Error decoding pyproject.toml: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
