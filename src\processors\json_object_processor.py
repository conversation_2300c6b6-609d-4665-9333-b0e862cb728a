"""
Cluster object processor for ID-based object resolution.

This module provides functionality to process axes and filters in cluster mode
where objects are specified by IDs instead of names.
"""

import logging
from typing import Dict, List, Optional, Any
from src.models.axis import AxisData, FilterData, FactsData
from src.models.kpi import JobParameters

try:
    from src.utils.cluster_api_client import ClusterAPIClient

    CLUSTER_API_AVAILABLE = True
except ImportError:
    CLUSTER_API_AVAILABLE = False
    ClusterAPIClient = None
from src.utils.data_processing import run_async_with_event_loop, escape_sql_string
from magic_gateway_client import AsyncMagicGatewayClient


class JsonObjectProcessor:
    """
    Processor for handling axes and filters in cluster mode.

    This processor resolves object definitions and DDL queries using cluster API endpoints
    when objects are specified by IDs instead of names.
    """

    def __init__(self, cluster_api_url: Optional[str] = None):
        """
        Initialize the cluster object processor.

        Args:
            cluster_api_url: Base URL for cluster API (defaults to config.job_api_url)
        """
        self.logger = logging.getLogger(__name__)
        self.cluster_api_url = cluster_api_url

    def is_json_mode_request(self, job_parameters: JobParameters) -> bool:
        """
        Determine if this is json based request.

        Args:
            job_parameters: Job parameters to check

        Returns:
            True if json mode should be used, False otherwise
        """
        # Check if axes contain ID-based specifications
        if job_parameters.axes:
            for axis_key, axis_value in job_parameters.axes.items():
                # If axis_value is an integer or AxisData with axis_id, it's json mode
                if isinstance(axis_value, int):
                    self.logger.info(f"Found json mode axis: {axis_key}")
                    return True
                elif (
                    isinstance(axis_value, AxisData) and axis_value.axis_id is not None
                ):
                    self.logger.info(f"Found json mode axis in AxisData: {axis_key}")
                    return True
                elif isinstance(axis_value, dict) and "axis_id" in axis_value:
                    self.logger.info(f"Found json mode axis in dict: {axis_key}")
                    return True

        self.logger.info(
            "Not a json mode request. Using standard name-based resolution"
        )
        return False

    def extract_object_ids(self, job_parameters: JobParameters) -> List[Dict[int, str]]:
        """
        Extract object IDs from job parameters.

        Args:
            job_parameters: Job parameters containing object specifications

        Returns:
            List of object IDs and types
        """
        axis_ids = []
        filter_ids = []

        # Extract axis IDs
        if job_parameters.axes:
            for axis_key, axis_value in job_parameters.axes.items():
                if isinstance(axis_value, int):
                    axis_ids.append(axis_value)
                elif (
                    isinstance(axis_value, AxisData) and axis_value.axis_id is not None
                ):
                    axis_ids.append(axis_value.axis_id)
                elif isinstance(axis_value, dict) and "axis_id" in axis_value:
                    axis_ids.append(axis_value["axis_id"])

        # Extract filter IDs
        if job_parameters.filters:
            for filter_key, filter_value in job_parameters.filters.items():
                if isinstance(filter_value, int):
                    filter_ids.append(filter_value)
                elif (
                    isinstance(filter_value, FilterData)
                    and filter_value.filter_id is not None
                ):
                    filter_ids.append(filter_value.filter_id)
                elif isinstance(filter_value, dict) and "filter_id" in filter_value:
                    filter_ids.append(filter_value["filter_id"])

        return [{"id": axis_id, "type": "axis"} for axis_id in axis_ids] + [
            {"id": filter_id, "type": "filter"} for filter_id in filter_ids
        ]

    async def resolve_json_objects(self, job_parameters: JobParameters) -> bool:
        """
        Resolve json objects using API endpoints.

        Args:
            job_parameters: Job parameters to populate with resolved objects

        Returns:
            True if successful, False otherwise
        """
        try:
            # Extract object IDs
            objects = self.extract_object_ids(job_parameters) or []

            if not objects and not job_parameters.facts_axis:
                self.logger.warning("No object IDs or facts found for json resolution")
                return False

            self.logger.info(f"Resolving {len(objects)} objects in json mode")

            # Create cluster API client
            async with ClusterAPIClient(self.cluster_api_url) as client:
                # Resolve object descriptions for axes and filters
                resolved_objects = {}
                if objects:
                    resolved_objects = await client.resolve_multiple_objects(
                        [obj["id"] for obj in objects]
                    )

                    if not resolved_objects:
                        self.logger.error(
                            "Failed to resolve any objects from cluster API"
                        )
                        return False

                # Resolve DDL translations for axes and filters
                axis_ddl = {}  # DDL translations for axes
                filter_ddl = {}  # DDL translations for filters
                if objects:
                    ddl_translations = await client.resolve_multiple_ddl(
                        {obj["id"]: obj["type"] for obj in objects},
                        cluster=job_parameters.cluster_mode,
                        id_panel=job_parameters.id_panel,
                    )
                    # Create object type lookup dictionary
                    obj_type_map = {obj["id"]: obj["type"] for obj in objects}
                    for obj_id, ddl_translation in ddl_translations.items():
                        if obj_type_map[obj_id] == "axis":
                            axis_ddl[obj_id] = ddl_translation
                        elif obj_type_map[obj_id] == "filter":
                            filter_ddl[obj_id] = ddl_translation

                # Process resolved axes
                if objects:
                    await self._process_resolved_axes(
                        job_parameters, resolved_objects, axis_ddl
                    )

                # Process resolved filters
                if objects:
                    await self._process_resolved_filters(
                        job_parameters, resolved_objects, filter_ddl
                    )

                # Process facts axis data if present
                if job_parameters.facts_axis:
                    job_parameters.facts_axis = (
                        await self.get_measures_data_with_mg_client(
                            job_parameters.facts_axis
                        )
                    )

                self.logger.info(
                    f"Successfully processed {len(job_parameters.axes)} axes, "
                    f"{len(job_parameters.filters)} filters, and "
                    f"{len(job_parameters.facts_axis) if job_parameters.facts_axis else 0} facts in json mode"
                )
                return True

        except Exception as e:
            self.logger.error(f"Error resolving cluster objects: {e}", exc_info=True)
            return False

    async def _process_resolved_axes(
        self,
        job_parameters: JobParameters,
        resolved_objects: Dict[int, Any],
        axis_ddl: Dict[int, Any],
    ):
        """
        Process resolved axes and populate AxisData models.

        Args:
            job_parameters: Job parameters to update
            resolved_objects: Resolved object definitions
            axis_ddl: Resolved DDL translations
        """
        if not job_parameters.axes:
            return

        updated_axes = {}

        for axis_key, axis_value in job_parameters.axes.items():
            # Extract axis ID
            axis_id = None
            if isinstance(axis_value, int):
                axis_id = axis_value
            elif isinstance(axis_value, AxisData) and axis_value.axis_id is not None:
                axis_id = axis_value.axis_id
            elif isinstance(axis_value, dict) and "axis_id" in axis_value:
                axis_id = axis_value["axis_id"]

            if axis_id is None or axis_id not in resolved_objects:
                self.logger.warning(
                    f"Could not resolve axis {axis_key} with ID {axis_id}"
                )
                continue

            # Get resolved object definition
            obj_def = resolved_objects[axis_id]

            # Process labels from position_list
            processed_labels = []
            for position in obj_def.position_list:
                label = {
                    "position_number": position.get("position_number"),
                    "position_name": position.get("position_name"),
                    "value_group_name": position.get("value_group_name"),
                }

                # Escape single quotes in value_group_name
                if label["value_group_name"]:
                    label["value_group_name"] = escape_sql_string(
                        label["value_group_name"]
                    )

                processed_labels.append(label)

            # Get DDL information
            ddl_info = {"cte": "", "queries": []}
            if axis_id in axis_ddl:
                ddl_translation = axis_ddl[axis_id]
                ddl_info = {
                    "cte": ddl_translation.cte,
                    "queries": list(
                        ddl_translation.queries.values()
                    ),  # Convert dict to list
                }

            # Create or update AxisData
            if isinstance(axis_value, AxisData):
                # Update existing AxisData
                axis_value.axis_id = axis_id
                axis_value.name = obj_def.name
                axis_value.database = obj_def.client_group
                axis_value.full_name = obj_def.client_group + "." + obj_def.name
                axis_value.type = obj_def.type
                axis_value.author=obj_def.author
                axis_value.last_update=obj_def.last_update
                axis_value.axis_positions = [len(obj_def.position_list)]
                axis_value.labels = processed_labels
                axis_value.ddl = ddl_info
                updated_axes[axis_key] = axis_value
            else:
                # Create new AxisData
                updated_axes[axis_key] = AxisData(
                    axis_id=axis_id,
                    name=obj_def.name,
                    type=obj_def.type,
                    full_name=obj_def.client_group + "." + obj_def.name,
                    database=obj_def.client_group,
                    author=obj_def.author,
                    last_update=obj_def.last_update,
                    axis_positions=[len(obj_def.position_list)],
                    labels=processed_labels,
                    ddl=ddl_info,
                )

            self.logger.info(
                f"Processed axis {axis_key}: {obj_def.name} (ID: {axis_id}, "
                f"type: {obj_def.type}, positions: {len(obj_def.position_list)})"
            )

        # Update job parameters
        job_parameters.axes = updated_axes

    async def _process_resolved_filters(
        self, job_parameters: JobParameters, resolved_objects: Dict[int, Any], filter_ddl: Dict[int, Any],
    ):
        """
        Process resolved filters and populate FilterData models.

        Args:
            job_parameters: Job parameters to update
            resolved_objects: Resolved object definitions
        """
        if not job_parameters.filters:
            return

        updated_filters = {}

        for filter_key, filter_value in job_parameters.filters.items():
            # Extract filter ID
            filter_id = None
            if isinstance(filter_value, int):
                filter_id = filter_value
            elif (
                isinstance(filter_value, FilterData)
                and filter_value.filter_id is not None
            ):
                filter_id = filter_value.filter_id
            elif isinstance(filter_value, dict) and "filter_id" in filter_value:
                filter_id = filter_value["filter_id"]

            if filter_id is None or filter_id not in resolved_objects:
                self.logger.warning(
                    f"Could not resolve filter {filter_key} with ID {filter_id}"
                )
                continue

            # Get resolved object definition
            obj_def = resolved_objects[filter_id]

            # For filters, we don't have DDL translation endpoint, so we use empty DDL
            ddl_info = {"cte": "", "queries": []}
            if filter_id in filter_ddl:
                ddl_translation = filter_ddl[filter_id]
                ddl_info = {
                    "cte": ddl_translation.cte,
                    "queries": list(ddl_translation.queries.values()),
                }

            # Create or update FilterData
            if isinstance(filter_value, FilterData):
                # Update existing FilterData
                filter_value.filter_id = filter_id
                filter_value.name = obj_def.name
                filter_value.type = obj_def.type
                filter_value.full_name = obj_def.client_group + "." + obj_def.name
                filter_value.database = obj_def.client_group
                filter_value.author=obj_def.author
                filter_value.last_update=obj_def.last_update
                filter_value.ddl = ddl_info
                updated_filters[filter_key] = filter_value
            else:
                # Create new FilterData
                filter_data = FilterData(
                    filter_id=filter_id,
                    name=obj_def.name,
                    type=obj_def.type,
                    full_name=obj_def.client_group + "." + obj_def.name,
                    database=obj_def.client_group,
                    author=obj_def.author,
                    last_update=obj_def.last_update,
                    ddl=ddl_info,
                )
                updated_filters[filter_key] = filter_data

            self.logger.info(
                f"Processed filter {filter_key}: {obj_def.name} (ID: {filter_id}, type: {obj_def.type})"
            )

        # Update job parameters
        job_parameters.filters = updated_filters

    def process_json_objects(self, job_parameters: JobParameters) -> bool:
        """
        Process cluster objects synchronously.

        Args:
            job_parameters: Job parameters to process

        Returns:
            True if successful, False otherwise
        """
        if not CLUSTER_API_AVAILABLE:
            self.logger.warning(
                "Cluster API client not available (aiohttp not installed). Falling back to standard mode."
            )
            return False

        try:
            # Use the utility function to handle event loop complexities
            coroutine = self.resolve_json_objects(job_parameters)
            result = run_async_with_event_loop(coroutine)
            return result if result is not None else False
        except Exception as e:
            self.logger.error(f"Error in json_processor: {e}", exc_info=True)
            return False

    async def get_measures_data_with_mg_client(
        self, facts_axis: Optional[List[FactsData]]
    ) -> List[FactsData]:
        """
        Get measure metadata for all measures in facts_axis using the AsyncMagicGatewayClient.
        This method retrieves measure information from the metadata.ctlg_measures table in PostgreSQL
        for cluster environments.

        Args:
            facts_axis: List of FactsData models containing measure information (id required)

        Returns:
            List of FactsData models with enriched measure data from PostgreSQL
        """
        if facts_axis is None or not facts_axis:
            return []

        # Get API credentials from config
        from src.core.config import MagicGatewaySettings

        mg_settings = MagicGatewaySettings()
        URL = mg_settings.base_url
        USER = mg_settings.username
        PASSWORD = mg_settings.password

        # Check if credentials are available
        if not URL or not USER or not PASSWORD:
            self.logger.error(
                "Missing required credentials for AsyncMagicGatewayClient"
            )
            return list(
                facts_axis
            )  # Return original facts_axis if credentials are missing

        # Ensure all facts are FactsData models, convert dictionaries if needed
        converted_facts_axis = []
        for fact in facts_axis:
            if isinstance(fact, dict):
                try:
                    converted_facts_axis.append(FactsData(**fact))
                    self.logger.debug(f"Converted dict to FactsData: {fact}")
                except Exception as convert_error:
                    self.logger.error(
                        f"Error converting fact dict to FactsData: {convert_error}"
                    )
                    continue
            elif isinstance(fact, FactsData):
                converted_facts_axis.append(fact)
            else:
                self.logger.warning(f"Unexpected fact type: {type(fact)}, skipping")
                continue

        if not converted_facts_axis:
            self.logger.warning("No valid facts after conversion")
            return []

        enriched_facts_axis = []

        try:
            # Create MG client
            mg_client = AsyncMagicGatewayClient(
                base_url=URL,
                username=USER,
                password=PASSWORD,
            )

            # Extract measure IDs from facts_axis
            measure_ids = [
                str(fact.id) for fact in converted_facts_axis if hasattr(fact, "id")
            ]

            if not measure_ids:
                self.logger.warning("No valid measure IDs found in facts_axis")
                # Return original facts_axis
                return list(facts_axis)

            # Construct a query to get measure data from PostgreSQL
            query = f"""
            SELECT
                id,
                code_name,
                display_name,
                required_facts,
                formula,
                divisor
            FROM metadata.ctlg_measures
            WHERE id IN ({",".join(measure_ids)})
            """

            # Execute the query using the AsyncMagicGatewayClient
            result = await mg_client.postgres.execute_query(query)

            # Create a mapping of measure ID to PostgreSQL data
            pg_measures_data = {}
            for row in result.rows:
                measure_id = row.get("id")
                if measure_id is not None:
                    pg_measures_data[str(measure_id)] = row

            # Enrich the converted facts_axis with PostgreSQL data
            for fact in converted_facts_axis:
                if not hasattr(fact, "id"):
                    # Skip invalid facts
                    self.logger.warning(f"Skipping invalid fact: {fact}")
                    continue

                fact_id = fact.id

                if str(fact_id) in pg_measures_data:
                    # Add PostgreSQL data
                    pg_data = pg_measures_data[str(fact_id)]

                    # Create a new FactsData with updated data
                    updated_fact = FactsData(
                        id=fact_id,
                        relative_axis=fact.relative_axis,
                        relative_position=fact.relative_position,
                        code_name=pg_data.get("code_name", fact.code_name),
                        display_name=pg_data.get("display_name", fact.display_name),
                        required_facts=pg_data.get(
                            "required_facts", fact.required_facts
                        ),
                        formula=pg_data.get("formula", fact.formula),
                        divisor=pg_data.get("divisor", fact.divisor),
                    )

                    enriched_facts_axis.append(updated_fact)
                else:
                    # If no PostgreSQL data found, keep the original fact
                    enriched_facts_axis.append(fact)

            self.logger.info(
                f"Enriched {len(enriched_facts_axis)} measures with PostgreSQL data"
            )
            return enriched_facts_axis

        except Exception as e:
            self.logger.error(
                f"Error getting measures data using mg_client in json mode: {e}",
                exc_info=True,
            )
            # Return the original facts_axis if there's an error, but ensure they are FactsData models
            if facts_axis:
                # Convert any dictionaries to FactsData models
                result_facts = []
                for fact in facts_axis:
                    if isinstance(fact, dict):
                        try:
                            result_facts.append(FactsData(**fact))
                        except Exception as convert_error:
                            self.logger.error(
                                f"Error converting fact dict to FactsData: {convert_error}"
                            )
                            # Skip invalid facts
                            continue
                    elif isinstance(fact, FactsData):
                        result_facts.append(fact)
                    else:
                        self.logger.warning(
                            f"Unexpected fact type: {type(fact)}, skipping"
                        )
                        continue
                return result_facts
            return []
