# Storage Processors

This directory contains specialized storage processors for the KPI Click application. Each processor handles a specific type of storage operation in ClickHouse environments.

## Architecture Overview

The storage processors follow a modular design where each processor is responsible for a specific aspect of data storage:

- **BaseStorageProcessor**: Common functionality shared by all processors
- **LocalStorageProcessor**: Local ClickHouse storage operations
- **ClusterStorageProcessor**: Cluster storage operations and remote data access
- **DistributedStorageProcessor**: Distributed table operations for high availability
- **MetadataStorageProcessor**: Job metadata management and status tracking

## Processors

### BaseStorageProcessor

**File**: `base_storage_processor.py`

**Purpose**: Provides common functionality for all storage processors.

**Key Features**:
- Table existence checking
- Table structure retrieval
- Row counting
- SQL string escaping
- Data transfer operations
- Query ID generation

**Usage**:
```python
# Not used directly - inherited by other processors
class CustomProcessor(BaseStorageProcessor):
    def __init__(self, connection):
        super().__init__(connection)
```

### LocalStorageProcessor

**File**: `local_storage_processor.py`

**Purpose**: Handles storage operations on the local ClickHouse instance.

**Key Features**:
- Local table creation with optimized structure
- Data transfer from temporary to permanent tables
- Local storage validation
- Temporary table cleanup

**Usage**:
```python
from src.processors.storage import LocalStorageProcessor

processor = LocalStorageProcessor(connection)

# Create permanent table
success = processor.create_permanent_table("table_name", structure)

# Transfer data
result = processor.transfer_temp_to_permanent(
    temp_table_name="temp_table",
    result_id="result_123",
    period=period_obj
)

# Validate storage
validation = processor.validate_storage("table_name")
```

### ClusterStorageProcessor

**File**: `cluster_storage_processor.py`

**Purpose**: Handles storage operations on ClickHouse cluster instances.

**Key Features**:
- Cluster table creation and management
- Remote data access using `remote()` function
- Cluster-to-single-node data transfer
- Cluster storage validation

**Usage**:
```python
from src.processors.storage import ClusterStorageProcessor

processor = ClusterStorageProcessor(
    connection=single_node_connection,
    cluster_connection=cluster_connection
)

# Transfer to cluster storage
result = processor.transfer_to_cluster_storage(
    temp_table_name="temp_table",
    result_id="result_123",
    period=period_obj
)

# Transfer from cluster to single node
result = processor.transfer_cluster_to_single_node(
    cluster_table_name="cluster_table",
    result_id="result_123",
    period=period_obj
)
```

### DistributedStorageProcessor

**File**: `distributed_storage_processor.py`

**Purpose**: Handles distributed table operations for ClickHouse cluster environments.

**Key Features**:
- Distributed table creation with proper sharding
- Data replication across cluster nodes
- High availability storage operations
- Direct result table creation

**Usage**:
```python
from src.processors.storage import DistributedStorageProcessor

processor = DistributedStorageProcessor(
    connection=single_node_connection,
    cluster_connection=cluster_connection
)

# Create distributed table
distributed_name = processor.create_distributed_table(
    local_table_name="local_table",
    structure=table_structure
)

# Create result table directly
result = processor.create_distributed_result_table(
    result_id="result_123",
    result_table_query="SELECT * FROM source",
    result_table_structure_query="CREATE TABLE ...",
    query_id="query_123",
    period=period_obj
)
```

### MetadataStorageProcessor

**File**: `metadata_storage_processor.py`

**Purpose**: Handles metadata storage operations including job metadata management.

**Key Features**:
- Job metadata initialization and updates
- Status tracking and management
- Job information serialization
- Metadata table operations

**Usage**:
```python
from src.processors.storage import MetadataStorageProcessor

processor = MetadataStorageProcessor(connection)

# Initialize job metadata
success = processor.initialize_job_metadata(
    job_parameters=job_params,
    username="user",
    result_id="result_123"
)

# Update job metadata
success = processor.update_job_metadata(
    result_id="result_123",
    status="in_progress",
    rows_transferred=1000
)

# Get job status
status = processor.get_job_status("result_123")

# Finalize job
success = processor.finalize_job_metadata(
    result_id="result_123",
    status="done",
    result_table_name="final_table"
)
```

## Design Principles

### Single Responsibility
Each processor has a clear, single responsibility:
- LocalStorageProcessor: Local storage only
- ClusterStorageProcessor: Cluster operations only
- DistributedStorageProcessor: Distributed tables only
- MetadataStorageProcessor: Metadata only

### Dependency Injection
Processors receive their dependencies through constructors:
```python
processor = LocalStorageProcessor(
    connection=connection,
    msg_logger_func=logger_func,
    progress_tracker=tracker
)
```

### Error Handling
All processors implement comprehensive error handling:
- Graceful degradation when features are unavailable
- Detailed error messages with context
- Return structured error information
- Logging at appropriate levels

### Interface Consistency
All processors follow consistent interfaces:
- Similar method signatures
- Consistent return value formats
- Standard error handling patterns
- Common logging practices

## Common Patterns

### Return Value Format
Most operations return dictionaries with consistent structure:
```python
{
    "success": True/False,
    "error": None or error_message,
    "rows_transferred": number,
    "permanent_table": table_name,
    "transfer_query_id": query_id,
    "storage_location": "local"/"cluster"
}
```

### Error Handling Pattern
```python
try:
    # Perform operation
    result = some_operation()
    return {"success": True, "data": result}
except Exception as e:
    error_msg = f"Operation failed: {str(e)}"
    self.logger.error(error_msg)
    return {"success": False, "error": error_msg}
```

### Validation Pattern
```python
def validate_something(self, param):
    """Validate parameter and return structured result."""
    validation_result = {
        "valid": False,
        "errors": [],
        "warnings": []
    }
    
    # Perform validation
    if not param:
        validation_result["errors"].append("Parameter is required")
        return validation_result
    
    validation_result["valid"] = True
    return validation_result
```

## Testing

Each processor can be tested independently:

```python
def test_local_storage_processor():
    mock_connection = Mock()
    processor = LocalStorageProcessor(mock_connection)
    
    # Test table existence
    mock_connection.get_query_dataframe.return_value = Mock(empty=False)
    assert processor.table_exists("test_table") is True
    
    # Test table creation
    assert processor.create_permanent_table("test_table", structure) is True
```

## Performance Considerations

### Local Storage
- Optimized for single-node operations
- Efficient table creation with proper ORDER BY
- Minimal overhead for local operations

### Cluster Storage
- Uses `remote()` function for efficient cluster access
- Batched operations where possible
- Connection pooling for cluster operations

### Distributed Storage
- Proper sharding configuration
- Optimized replication settings
- Efficient distributed table creation

### Metadata Storage
- Efficient JSON serialization
- Indexed metadata tables
- Minimal metadata overhead

## Extension Points

### Adding New Processors
1. Inherit from `BaseStorageProcessor`
2. Implement required methods
3. Add to `__init__.py`
4. Update `StorageService` to use new processor

### Custom Storage Types
```python
class CustomStorageProcessor(BaseStorageProcessor):
    def __init__(self, connection, custom_config):
        super().__init__(connection)
        self.custom_config = custom_config
    
    def custom_operation(self, params):
        # Implement custom storage logic
        pass
```

### Configuration Extensions
Processors can be extended with custom configuration:
```python
processor = LocalStorageProcessor(
    connection=connection,
    custom_settings={
        "table_engine": "ReplacingMergeTree",
        "compression": "ZSTD"
    }
)
```

## Best Practices

1. **Use Appropriate Processor**: Choose the right processor for your use case
2. **Handle Errors**: Always check return values and handle errors
3. **Validate Inputs**: Validate parameters before processing
4. **Log Operations**: Use appropriate logging levels
5. **Clean Up Resources**: Clean up temporary resources
6. **Test Thoroughly**: Test both success and failure cases
7. **Monitor Performance**: Monitor processor performance in production

## Troubleshooting

### Common Issues
1. **Connection Errors**: Check connection configuration
2. **Permission Issues**: Verify database permissions
3. **Table Not Found**: Check table existence before operations
4. **Cluster Connectivity**: Verify cluster configuration

### Debug Information
Enable debug logging to get detailed information:
```python
import logging
logging.getLogger('src.processors.storage').setLevel(logging.DEBUG)
```

### Performance Issues
- Check connection pool settings
- Monitor query execution times
- Verify table structures are optimized
- Check cluster network connectivity