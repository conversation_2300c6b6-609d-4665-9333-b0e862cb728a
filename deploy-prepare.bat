@echo off
REM ============================================================================
REM KPI Click - Windows Deployment Preparation Script
REM ============================================================================
REM This script builds the Docker image and prepares it for deployment to server
REM Usage: deploy-prepare.bat [tag] [project_id] [job_token]
REM ============================================================================

setlocal enabledelayedexpansion

REM Default configuration
set "IMAGE_NAME=localhost/kpi_click"
set "IMAGE_TAG=%~1"
set "PROJECT_ID=%~2"
set "JOB_TOKEN=%~3"
set "DEPLOYMENT_DIR=deployment"
set "TIMESTAMP="

REM Set default tag if not provided
if "%IMAGE_TAG%"=="" set "IMAGE_TAG=latest"

REM Set default GitLab credentials if not provided
if "%PROJECT_ID%"=="" set "PROJECT_ID=mg_client.git"
if "%JOB_TOKEN%"=="" set "JOB_TOKEN=**************************"

REM Generate timestamp for unique filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%"

echo ============================================================================
echo KPI Click Deployment Preparation
echo ============================================================================
echo Image Name: %IMAGE_NAME%
echo Image Tag: %IMAGE_TAG%
echo Project ID: %PROJECT_ID%
echo Timestamp: %TIMESTAMP%
echo ============================================================================

REM Check if Docker is available
where docker >nul 2>nul
if %errorlevel% equ 0 (
    set "CONTAINER_CMD=docker"
    echo Using Docker for container operations
) else (
    where podman >nul 2>nul
    if %errorlevel% equ 0 (
        set "CONTAINER_CMD=podman"
        echo Using Podman for container operations fallback
    ) else (
        echo ERROR: Neither Docker nor Podman found in PATH
        echo Please install Docker before running this script
        exit /b 1
    )
)

REM Create deployment directory if it doesn't exist
if not exist "%DEPLOYMENT_DIR%" (
    echo Creating deployment directory: %DEPLOYMENT_DIR%
    mkdir "%DEPLOYMENT_DIR%"
)

REM Clean up old images optional - remove if you want to keep them
echo.
echo Cleaning up old container images...
%CONTAINER_CMD% image prune -f >nul 2>nul

REM Build the container image
echo.
echo ============================================================================
echo Building container image: %IMAGE_NAME%:%IMAGE_TAG%
echo ============================================================================
%CONTAINER_CMD% build --build-arg CI_PROJECT_ID="%PROJECT_ID%" --build-arg CI_JOB_TOKEN="%JOB_TOKEN%" -t "%IMAGE_NAME%:%IMAGE_TAG%" .

if %errorlevel% neq 0 (
    echo ERROR: Container build failed
    exit /b 1
)

echo.
echo Container build completed successfully!

REM Save the image to tar file
REM Replace forward slash with underscore for filename
set "SAFE_IMAGE_NAME=%IMAGE_NAME:/=_%"
set "TAR_FILENAME=%SAFE_IMAGE_NAME%-app-%IMAGE_TAG%-%TIMESTAMP%.tar"
set "TAR_FILEPATH=%DEPLOYMENT_DIR%\%TAR_FILENAME%"

echo.
echo ============================================================================
echo Saving container image to: %TAR_FILEPATH%
echo ============================================================================
%CONTAINER_CMD% save "%IMAGE_NAME%:%IMAGE_TAG%" -o "%TAR_FILEPATH%"

if %errorlevel% neq 0 (
    echo ERROR: Failed to save container image
    exit /b 1
)

REM Get file size for verification
for %%A in ("%TAR_FILEPATH%") do set "FILE_SIZE=%%~zA"
set /a "FILE_SIZE_MB=%FILE_SIZE% / 1048576"

echo.
echo ============================================================================
echo Deployment preparation completed successfully!
echo ============================================================================
echo Container image saved to: %TAR_FILEPATH%
echo File size: %FILE_SIZE_MB% MB
echo.
echo Next steps:
echo 1. Copy %TAR_FILENAME% to your target server
echo 2. Run the deploy-run.sh script on the server
echo 3. Verify the application is running correctly
echo ============================================================================

REM Create a simple deployment info file
set "INFO_FILE=%DEPLOYMENT_DIR%\deployment-info-%TIMESTAMP%.txt"
echo Deployment Information > "%INFO_FILE%"
echo ====================== >> "%INFO_FILE%"
echo Build Date: %date% %time% >> "%INFO_FILE%"
echo Image Name: %IMAGE_NAME% >> "%INFO_FILE%"
echo Image Tag: %IMAGE_TAG% >> "%INFO_FILE%"
echo Archive File: %TAR_FILENAME% >> "%INFO_FILE%"
echo File Size: %FILE_SIZE_MB% MB >> "%INFO_FILE%"
echo Project ID: %PROJECT_ID% >> "%INFO_FILE%"

echo Deployment info saved to: %INFO_FILE%

REM Ask user if they want to open the deployment folder
choice /M "Do you want to open the deployment folder"
if errorlevel 2 goto :end
echo.
echo Opening deployment folder...
start "" "%DEPLOYMENT_DIR%"

:end
endlocal
