"""
Formatting utilities.

This module provides utilities for formatting various types of data.
"""

import json
import logging

import numpy as np

# Set up logging
logger = logging.getLogger(__name__)


def format_duration(milliseconds: float) -> str:
    """Converts milliseconds to minutes and seconds.

    Args:
        milliseconds: Duration in milliseconds

    Returns:
        str: Formatted duration string in the format "Xm Y.ZZs"
    """
    minutes, seconds = divmod(milliseconds / 1000, 60)
    return f"{int(minutes)}m {seconds:.2f}s"


def format_bytes(size_in_bytes: float) -> str:
    """Converts bytes to a human-readable format (MB or GB).

    Args:
        size_in_bytes: Size in bytes

    Returns:
        str: Human-readable size string
    """
    if size_in_bytes >= 1_073_741_824:  # 1 GB
        return f"{size_in_bytes / 1_073_741_824:.2f} GB"
    if size_in_bytes >= 1_048_576:  # 1 MB
        return f"{size_in_bytes / 1_048_576:.2f} MB"
    return f"{size_in_bytes} Bytes"


# Custom JSON encoder to handle NumPy types and Pydantic models
class NumpyJSONEncoder(json.JSONEncoder):
    """
    JSON encoder that handles NumPy types and Pydantic models by converting them to Python standard types.

    This encoder handles:
    - NumPy types (integers, floats, arrays, booleans)
    - Pydantic models (using their model_dump or dict methods)
    - DictLikeModel instances (base class for all models in this application)
    - Model classes (by converting them to string representations)

    The encoder first distinguishes between model classes and model instances:
    - For model classes (type objects or ModelMetaclass), it returns a string representation
      instead of trying to call instance methods on them
    - For model instances, it proceeds with serialization

    For model instances, it uses multiple detection strategies:
    1. Checks if the object's class is from a module containing 'pydantic' or from our models
    2. Checks if the object has model_dump() or dict() methods as instance methods (not class methods)
    3. Checks if the object's class inherits from BaseModel or DictLikeModel

    It then tries multiple serialization methods in order:
    1. model_dump() for Pydantic v2
    2. dict() for Pydantic v1
    3. dict(obj.items()) for DictLikeModel
    4. obj.__dict__ as a last resort

    Each serialization method is wrapped in a try-except block with detailed error logging
    to help diagnose serialization issues.

    This provides robust serialization that works with different Pydantic versions
    and custom model implementations, while properly handling both model classes and instances.
    """

    def default(self, obj):
        try:
            # Handle NumPy types
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.bool_):
                return bool(obj)

            # Check if it's a class (type or metaclass) rather than an instance
            if isinstance(obj, type) or (
                hasattr(obj, "__class__") and obj.__class__.__name__ == "ModelMetaclass"
            ):
                # For model classes, return the class name as a string
                # This prevents trying to call instance methods on class objects
                logging.info(
                    f"Encountered a model class during serialization: {obj.__name__ if hasattr(obj, '__name__') else str(obj)}"
                )
                return f"<class '{obj.__name__ if hasattr(obj, '__name__') else str(obj)}'>"

            # Handle Pydantic models - but only if they're instances, not classes
            # First check: Is it a Pydantic model instance by module name?
            is_pydantic_by_module = (
                hasattr(obj, "__class__")
                and hasattr(obj.__class__, "__module__")
                and (
                    "pydantic" in obj.__class__.__module__
                    or "models.axis" in obj.__class__.__module__
                    or "models.kpi" in obj.__class__.__module__
                )
                # Make sure it's not a class itself
                and not isinstance(obj, type)
                and obj.__class__.__name__ != "ModelMetaclass"
            )

            # Second check: Does it have model_dump or dict methods as instance methods?
            has_model_methods = (
                hasattr(obj, "model_dump")
                and callable(obj.model_dump)
                and not isinstance(obj.model_dump, type)
            ) or (
                hasattr(obj, "dict")
                and callable(obj.dict)
                and not isinstance(obj.dict, type)
            )

            # Third check: Is it a BaseModel instance (not class)?
            is_base_model_instance = False
            if (
                hasattr(obj, "__class__")
                and not isinstance(obj, type)
                and obj.__class__.__name__ != "ModelMetaclass"
            ):
                for base in obj.__class__.__mro__:
                    if base.__name__ == "BaseModel" or base.__name__ == "DictLikeModel":
                        is_base_model_instance = True
                        break

            # If any of the checks pass, treat it as a Pydantic model instance
            if is_pydantic_by_module or (has_model_methods and is_base_model_instance):
                # Try model_dump first (Pydantic v2)
                if (
                    hasattr(obj, "model_dump")
                    and callable(obj.model_dump)
                    and not isinstance(obj.model_dump, type)
                ):
                    try:
                        return obj.model_dump()
                    except Exception as e:
                        logging.warning(
                            f"model_dump() failed for {type(obj).__name__}: {e}, trying alternative methods"
                        )

                # Fall back to dict() method (Pydantic v1)
                if (
                    hasattr(obj, "dict")
                    and callable(obj.dict)
                    and not isinstance(obj.dict, type)
                ):
                    try:
                        return obj.dict()
                    except Exception as e:
                        logging.warning(
                            f"dict() failed for {type(obj).__name__}: {e}, trying alternative methods"
                        )

                # Last resort: try to convert to dict directly if it has items() method
                if (
                    hasattr(obj, "items")
                    and callable(obj.items)
                    and not isinstance(obj.items, type)
                ):
                    try:
                        return dict(obj.items())
                    except Exception as e:
                        logging.warning(
                            f"items() conversion failed for {type(obj).__name__}: {e}, trying alternative methods"
                        )

                # If all else fails, try __dict__
                if hasattr(obj, "__dict__"):
                    return obj.__dict__

            # Handle DictLikeModel instances (for backward compatibility)
            if (
                hasattr(obj, "items")
                and hasattr(obj, "keys")
                and callable(obj.items)
                and callable(obj.keys)
                and not isinstance(obj.items, type)  # Make sure it's an instance method
                and not isinstance(obj.keys, type)  # Make sure it's an instance method
            ):
                if (
                    hasattr(obj, "__class__")
                    and hasattr(obj.__class__, "__name__")
                    and "Model" in obj.__class__.__name__
                    and not isinstance(obj, type)  # Make sure it's not a class
                ):
                    try:
                        # Convert to dictionary using items method
                        return dict(obj.items())
                    except Exception as e:
                        logging.warning(
                            f"dict(items()) failed for {type(obj).__name__}: {e}"
                        )

            # Default behavior for other types
            return super().default(obj)
        except Exception as e:
            # Log the error and provide more context about the object
            logging.error(
                f"JSON serialization error for object of type {type(obj).__name__}: {e}"
            )
            if hasattr(obj, "__dict__"):
                logging.error(f"Object attributes: {list(obj.__dict__.keys())}")
            # Re-raise as TypeError with more context
            raise TypeError(
                f"Object of type {type(obj).__name__} is not JSON serializable: {e}"
            ) from e
