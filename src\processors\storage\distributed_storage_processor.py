"""
Distributed storage processor for KPI results.

This module handles distributed table operations for ClickHouse cluster environments,
including distributed table creation, data replication, and high availability storage.
"""

import logging
from typing import Any, Dict, List, Optional

from src.core.clickhouse_connect import ClickHouseConnection
from src.models.axis import Period
from .base_storage_processor import BaseStorageProcessor


class DistributedStorageProcessor(BaseStorageProcessor):
    """
    Processor for ClickHouse distributed storage operations.
    
    Handles:
    - Distributed table creation and management
    - Data replication across cluster nodes
    - High availability storage operations
    - Distributed table validation
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        cluster_connection: Optional[ClickHouseConnection] = None,
        msg_logger_func=None,
        progress_tracker=None,
    ):
        """
        Initialize the distributed storage processor.

        Args:
            connection: Single-node ClickHouse connection instance
            cluster_connection: Optional cluster ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
        """
        super().__init__(connection, msg_logger_func, progress_tracker)
        self.cluster_connection = cluster_connection
        self.logger = logging.getLogger(__name__)

    def create_distributed_table(
        self, local_table_name: str, structure: List[Dict[str, str]]
    ) -> Optional[str]:
        """
        Create distributed table for cluster mode.
        
        Args:
            local_table_name: Name of the local table
            structure: List of column definitions with name and type
            
        Returns:
            Distributed table name if successful, None otherwise
        """
        try:
            if not self.cluster_connection:
                self.logger.warning("No cluster connection available for distributed table creation")
                return None

            from src.core.config import config

            # Extract table name without database prefix for distributed table
            if "." in local_table_name:
                database, table = local_table_name.split(".", 1)
                distributed_table_name = f"{database}.{table}_distributed"
            else:
                distributed_table_name = f"{local_table_name}_distributed"
                database = config.clickhouse_cluster_database
                table = local_table_name

            # Build column definitions
            column_defs = []
            for col in structure:
                col_name = col["name"].replace('"', "").replace("'", "").replace(" ", "_")
                col_type = col["type"]
                column_defs.append(f"`{col_name}` {col_type}")

            # Get cluster name from config
            cluster_name = config.clickhouse_cluster_name
            if not cluster_name:
                self.logger.warning("No cluster name configured, skipping distributed table creation")
                return None

            # Create distributed table query
            create_distributed_query = f"""
                CREATE TABLE IF NOT EXISTS {distributed_table_name} ON CLUSTER {cluster_name} (
                    {", ".join(column_defs)}
                )
                ENGINE = Distributed({cluster_name}, {database}, {table}, rand())
            """

            self.logger.info(f"Creating distributed table: {distributed_table_name}")
            self.logger.debug(f"Create distributed table query: {create_distributed_query}")

            # Execute on cluster connection
            self.cluster_connection.execute_command(create_distributed_query)
            self.logger.info(f"Successfully created distributed table {distributed_table_name}")
            
            return distributed_table_name

        except Exception as e:
            self.logger.error(f"Failed to create distributed table for {local_table_name}: {str(e)}")
            # Don't raise here as distributed table creation is optional
            return None

    def table_exists_on_cluster(self, table_name: str) -> bool:
        """Check if a table exists on cluster."""
        try:
            if not self.cluster_connection:
                return False

            return self.table_exists(table_name, self.cluster_connection)

        except Exception as e:
            self.logger.error(f"Failed to check if table {table_name} exists on cluster: {str(e)}")
            return False

    def create_distributed_result_table(
        self,
        result_id: str,
        result_table_query: str,
        result_table_structure_query: str,
        query_id: str,
        period: Period,
        cluster_database: str = "job_result",
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Create distributed result table directly using provided queries.
        
        Args:
            result_id: Combined result ID for the job
            result_table_query: SQL query to populate the result table
            result_table_structure_query: SQL query for creating table structure
            query_id: Unique query ID for tracking
            period: Period information
            cluster_database: Database name on cluster
            **kwargs: Additional parameters
            
        Returns:
            Dictionary with creation status information
        """
        try:
            if not self.cluster_connection:
                error_msg = "No cluster connection available for distributed result table creation"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            from src.core.config import config

            cluster_name = config.clickhouse_cluster_name
            if not cluster_name:
                error_msg = "No cluster name configured"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Define table names
            local_table = f"{cluster_database}.{result_id}"

            self.logger.info(f"Creating distributed result table for {result_id}")
            
            # Check if result table already exists
            if not self.table_exists_on_cluster(local_table + "_distributed"):
                # Create table structure on cluster
                self.logger.info("Creating replicated table on cluster")
                replicated_table = self.cluster_connection.create_replicated_table(
                    table_query=result_table_structure_query,
                    table_name=local_table,
                    template=True,
                )
                # Create distributed table
                distributed_table = self.cluster_connection.create_distributed_table(replicated_table, local_table)
                if not distributed_table:
                    self.logger.warning("Failed to create distributed table, continuing with local table only")
            else:
                replicated_table = local_table + "_replicated"
                distributed_table = local_table + "_distributed"

            # Populate the table with data
            self.logger.info("Populating result table with data")
            settings = {"query_id": query_id}
            self.cluster_connection.add_rows_from_query(distributed_table, result_table_query, settings=settings)

            # Count rows in the result table
            count_query = f"SELECT count() as cnt FROM {distributed_table}"
            count_result = self.cluster_connection.get_query_dataframe(count_query)
            row_count = count_result.iloc[0]["cnt"] if not count_result.empty else 0

            self.logger.info(f"Successfully created distributed result table with {row_count} rows")

            return {
                "success": True,
                "error": None,
                "rows_transferred": row_count,
                "permanent_table": distributed_table,
                "distributed_table": distributed_table,
                "replicated_table": replicated_table,
                "query_id": query_id,
                "storage_location": "cluster",
                "creation_type": "direct_distributed",
            }

        except Exception as e:
            error_msg = f"Failed to create distributed result table for {result_id}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def get_distributed_table_name(self, local_table_name: str) -> Optional[str]:
        """
        Get the distributed table name for a given local table name.

        Args:
            local_table_name: Name of the local table

        Returns:
            Distributed table name if available, None otherwise
        """
        try:
            # Generate distributed table name
            if "." in local_table_name:
                database, table = local_table_name.split(".", 1)
                return f"{database}.{table}_distributed"
            else:
                return f"{local_table_name}_distributed"

        except Exception as e:
            self.logger.error(f"Failed to generate distributed table name for {local_table_name}: {str(e)}")
            return None

    def validate_distributed_storage(self, table_name: str) -> Dict[str, Any]:
        """
        Validate distributed storage table.
        
        Args:
            table_name: Name of the distributed table to validate
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation_result = {
                "table_exists": False,
                "row_count": 0,
                "structure_valid": False,
                "cluster_available": False,
                "error": None
            }

            # Check cluster availability
            if not self.cluster_connection:
                validation_result["error"] = "No cluster connection available"
                return validation_result

            validation_result["cluster_available"] = True

            # Check if distributed table exists
            if not self.table_exists_on_cluster(table_name):
                validation_result["error"] = f"Distributed table {table_name} does not exist"
                return validation_result

            validation_result["table_exists"] = True

            # Get row count
            validation_result["row_count"] = self.count_table_rows(table_name, self.cluster_connection)

            # Check structure
            structure = self.get_table_structure(table_name, self.cluster_connection)
            validation_result["structure_valid"] = structure is not None and len(structure) > 0

            self.logger.info(f"Distributed storage validation for {table_name}: {validation_result}")
            return validation_result

        except Exception as e:
            error_msg = f"Failed to validate distributed storage for {table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {
                "table_exists": False,
                "row_count": 0,
                "structure_valid": False,
                "cluster_available": False,
                "error": error_msg
            }