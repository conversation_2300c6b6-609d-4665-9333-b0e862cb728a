-- Results Metadata Table Schema
-- This table stores job-level metadata for KPI analysis results
-- One record per job_id (consolidating all periods)

CREATE TABLE IF NOT EXISTS metadata.results_metadata (
    -- Primary identifiers
    id String COMMENT 'Combined result ID for the entire job',
    job_id String COMMENT 'Original job ID from the system',
    final_result_table String COMMENT 'Name of the final result table',

    -- Job status tracking
    status Enum('in_progress' = 1, 'done' = 2, 'error' = 3) DEFAULT 'in_progress' COMMENT 'Current status of the job',
    
    -- Lifecycle status tracking for cleanup operations
    lifecycle_status Enum(
        'ACTIVE' = 1,      -- Current/valid result, actively used
        'OUTDATED' = 2,    -- Superseded by newer results from reruns
        'EXPIRED' = 3,     -- Exceeded retention period, marked for deletion
        'DELETED' = 4      -- Result tables have been deleted
    ) DEFAULT 'ACTIVE' COMMENT 'Lifecycle status for automated cleanup operations',
    
    -- Analysis information
    analysis_name String COMMENT 'Name of the KPI analysis',
    kpi_type String COMMENT 'Type of KPI (e.g., standard or catman)',
    id_panel UInt32 COMMENT 'Panel ID used for the analysis',
    
    -- Periods and Facts information 
    periods String COMMENT 'List of periods',
    facts String COMMENT 'List of facts (measures)',
    
    -- Query information
    query_ids String DEFAULT '' COMMENT 'List of all query IDs executed for this job',
    result_rows UInt64 COMMENT 'Total number of rows in the final result table',
    job_duration String COMMENT 'Total job duration in milliseconds',

    -- Timestamps
    created_at DateTime DEFAULT now() COMMENT 'When the job was created',
    completed_at DateTime COMMENT 'When the job was completed',
    
    -- Lifecycle tracking timestamps
    marked_for_deletion_at Nullable(DateTime) COMMENT 'When the job was marked for deletion',
    deleted_at Nullable(DateTime) COMMENT 'When the result tables were actually deleted',
    
    -- Job metadata and configuration
    username String DEFAULT '' COMMENT 'Username who initiated the job',
    job_info String DEFAULT '{}' COMMENT 'JSON containing all job details, periods, axes, filters',
    backend_version String DEFAULT '' COMMENT 'Version of the kpi+ backend',
    error_info String DEFAULT '{}' COMMENT 'JSON containing error information if job failed',
    retention_days UInt16 DEFAULT 30 COMMENT 'Number of days to retain the result',
    
    -- Cleanup tracking
    superseded_by String DEFAULT '' COMMENT 'ID of the job that superseded this one (for OUTDATED status)',
    cleanup_reason String DEFAULT '' COMMENT 'Reason for marking as expired/deleted (retention, manual, etc.)',
    
    -- Primary key
    PRIMARY KEY (id)
)
ENGINE = MergeTree()
TTL created_at + toIntervalDay(retention_days + 10)
ORDER BY (id, created_at)
SETTINGS index_granularity = 8192
COMMENT 'Metadata table for KPI analysis jobs with lifecycle management';
