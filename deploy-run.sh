#!/bin/bash
# ============================================================================
# KPI Click - Linux Server Deployment Script
# ============================================================================
# This script deploys and runs the KPI Click application on a Linux server
# Usage: ./deploy-run.sh [tar_file] [container_name] [port]
# ============================================================================

set -e  # Exit on error

# Configuration
IMAGE_NAME="localhost/kpi_click"
DEFAULT_CONTAINER_NAME="kpiclick-app"
DEFAULT_PORT="8000"
DEFAULT_TAR_PATTERN="kpi_click-app-*.tar"

# Parse command line arguments
TAR_FILE="$1"
CONTAINER_NAME="${2:-$DEFAULT_CONTAINER_NAME}"
PORT="${3:-$DEFAULT_PORT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to display help
show_help() {
    cat << EOF
KPI Click Deployment Script

Usage: $0 [tar_file] [container_name] [port]

Arguments:
  tar_file        Path to the container image tar file (optional - will auto-detect)
  container_name  Name for the container (default: $DEFAULT_CONTAINER_NAME)
  port           Port to expose the application (default: $DEFAULT_PORT)

Examples:
  $0                                          # Auto-detect tar file, use defaults
  $0 kpi_click-app-latest-20250809.tar       # Specify tar file
  $0 app.tar kpiclick-prod 9000              # Full specification

Environment Variables:
  KPI_CLICK_ENV_FILE    Path to environment file (default: .env)
  KPI_CLICK_DATA_DIR    Path to data directory for volumes
EOF
}

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detect container runtime
detect_container_runtime() {
    if command_exists docker; then
        echo "docker"
    elif command_exists podman; then
        echo "podman"
    else
        log_error "Neither Docker nor Podman found. Please install Docker."
        exit 1
    fi
}

# Auto-detect tar file if not provided
auto_detect_tar_file() {
    # Find the most recent tar file in current directory
    local tar_file
    tar_file=$(ls -t *.tar 2>/dev/null | head -1)
    
    if [ -z "$tar_file" ]; then
        log_error "No tar files found in current directory"
        log_info "Available files:"
        ls -la
        log_info "Please specify the tar file path as the first argument"
        exit 1
    fi
    
    echo "$tar_file"
}

# Function to validate tar file
validate_tar_file() {
    local tar_file="$1"
    
    if [ ! -f "$tar_file" ]; then
        log_error "Tar file not found: $tar_file"
        exit 1
    fi
    
    if [ ! -r "$tar_file" ]; then
        log_error "Cannot read tar file: $tar_file"
        exit 1
    fi
    
    # Check if it's a valid tar file
    if ! tar -tf "$tar_file" >/dev/null 2>&1; then
        log_error "Invalid tar file: $tar_file"
        exit 1
    fi
    
    log_success "Tar file validation passed: $tar_file"
}

# Function to stop and remove existing container
cleanup_existing_container() {
    local container_name="$1"
    local container_cmd="$2"
    
    # Check if container exists
    if $container_cmd ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        log_warning "Existing container found: $container_name"
        
        # Stop the container if it's running
        if $container_cmd ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
            log_info "Stopping running container: $container_name"
            $container_cmd stop "$container_name" || true
        fi
        
        # Remove the container
        log_info "Removing existing container: $container_name"
        $container_cmd rm "$container_name" || true
    fi
}

# Function to load container image
load_container_image() {
    local tar_file="$1"
    local container_cmd="$2"
    
    log_info "Loading container image from: $tar_file"
    
    # Remove existing image if it exists to avoid renaming
    if $container_cmd images --format "{{.Repository}}:{{.Tag}}" | grep -q "^${IMAGE_NAME}:latest$"; then
        log_info "Found existing image: ${IMAGE_NAME}:latest"
        
        # Find and remove containers using this image
        local containers_using_image
        containers_using_image=$($container_cmd ps -a --filter "ancestor=${IMAGE_NAME}:latest" --format "{{.ID}} {{.Names}}" 2>/dev/null || true)
        
        if [ -n "$containers_using_image" ]; then
            log_info "Removing containers using the image:"
            echo "$containers_using_image" | while read -r container_id container_name; do
                if [ -n "$container_id" ]; then
                    log_info "  Stopping and removing container: $container_name ($container_id)"
                    $container_cmd stop "$container_id" >/dev/null 2>&1 || true
                    $container_cmd rm "$container_id" >/dev/null 2>&1 || true
                fi
            done
        fi
        
        # Now remove the image
        log_info "Removing existing image: ${IMAGE_NAME}:latest"
        $container_cmd rmi "${IMAGE_NAME}:latest" || {
            log_warning "Could not remove image, forcing removal..."
            $container_cmd rmi -f "${IMAGE_NAME}:latest" || true
        }
    fi
    
    # Get file size for progress indication
    local file_size
    if command_exists du; then
        file_size=$(du -h "$tar_file" | cut -f1)
        log_info "Loading image ($file_size)..."
    fi
    
    if $container_cmd load -i "$tar_file"; then
        log_success "Container image loaded successfully"
    else
        log_error "Failed to load container image"
        exit 1
    fi
}

# Function to create environment file if it doesn't exist
setup_environment() {
    local env_file="${KPI_CLICK_ENV_FILE:-/scriptfolder/kpi_click/.env}"
    
    # Check if the directory exists
    local env_dir=$(dirname "$env_file")
    if [ ! -d "$env_dir" ]; then
        log_warning "Environment directory not found: $env_dir"
        log_info "Creating directory: $env_dir"
        mkdir -p "$env_dir" || {
            log_error "Failed to create directory: $env_dir"
            log_info "Falling back to current directory"
            env_file="./.env"
        }
    fi
    
    if [ ! -f "$env_file" ]; then
        log_warning "Environment file not found: $env_file"
        log_info "Creating basic environment file..."
        
        cat > "$env_file" << 'EOF'
# KPI Click Application Configuration
LOG_LEVEL=INFO
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=kpiclick_metadata
POSTGRES_USER=kpiclick
POSTGRES_PASSWORD=changeme
EOF
        
        log_warning "Basic environment file created. Please review and update: $env_file"
    fi
    
    echo "$env_file"
}

# Function to run the container
run_container() {
    local container_name="$1"
    local port="$2"
    local container_cmd="$3"
    local env_file="$4"
    
    log_info "Starting container: $container_name"
    log_info "Port mapping: $port:8000"
    log_info "Environment file: $env_file"
    
    # Prepare volume mounts
    local volume_args=""
    
    # Mount data directory if specified
    if [ -n "${KPI_CLICK_DATA_DIR:-}" ] && [ -d "$KPI_CLICK_DATA_DIR" ]; then
        volume_args="$volume_args -v $KPI_CLICK_DATA_DIR:/kpi_click/data"
        log_info "Mounting data directory: $KPI_CLICK_DATA_DIR"
    fi
    
    # Mount exports directory
    local exports_dir="./exports"
    if [ ! -d "$exports_dir" ]; then
        mkdir -p "$exports_dir"
        log_info "Created exports directory: $exports_dir"
    fi
    volume_args="$volume_args -v $(pwd)/exports:/kpi_click/exports"
    
    # Mount logs directory
    local logs_dir="./logs"
    if [ ! -d "$logs_dir" ]; then
        mkdir -p "$logs_dir"
        log_info "Created logs directory: $logs_dir"
    fi
    volume_args="$volume_args -v $(pwd)/logs:/kpi_click/memlog"
    
    # Run the container
    if $container_cmd run -d \
        --name "$container_name" \
        --env-file "$env_file" \
        -p "$port:8000" \
        $volume_args \
        --restart unless-stopped \
        "$IMAGE_NAME:latest"; then
        
        log_success "Container started successfully: $container_name"
        log_info "Application should be available on port: $port"
    else
        log_error "Failed to start container"
        exit 1
    fi
}

# Function to verify deployment
verify_deployment() {
    local container_name="$1"
    local port="$2"
    local container_cmd="$3"
    
    log_info "Verifying deployment..."
    
    # Check if container is running
    if ! $container_cmd ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        log_error "Container is not running: $container_name"
        log_info "Container logs:"
        $container_cmd logs "$container_name" || true
        exit 1
    fi
    
    # Wait a moment for the application to start
    sleep 5
    
    # Check container health
    local container_status
    container_status=$($container_cmd inspect "$container_name" --format '{{.State.Status}}' 2>/dev/null || echo "unknown")
    
    if [ "$container_status" = "running" ]; then
        log_success "Container is running successfully"
    else
        log_error "Container status: $container_status"
        log_info "Container logs:"
        $container_cmd logs "$container_name" || true
        exit 1
    fi
    
    # Test basic connectivity (if curl is available)
    if command_exists curl; then
        log_info "Testing application connectivity..."
        if curl -f -s "http://localhost:$port/health" >/dev/null 2>&1; then
            log_success "Application health check passed"
        else
            log_warning "Health check endpoint not responding (this may be normal if no health endpoint exists)"
        fi
    fi
}

# Function to show deployment summary
show_deployment_summary() {
    local container_name="$1"
    local port="$2"
    local container_cmd="$3"
    
    echo
    echo "============================================================================"
    log_success "KPI Click Deployment Completed Successfully!"
    echo "============================================================================"
    echo "Container Name: $container_name"
    echo "Port: $port"
    echo "Image: $IMAGE_NAME:latest"
    echo
    echo "Useful Commands:"
    echo "  View logs:     $container_cmd logs $container_name"
    echo "  Follow logs:   $container_cmd logs -f $container_name"
    echo "  Stop app:      $container_cmd stop $container_name"
    echo "  Start app:     $container_cmd start $container_name"
    echo "  Restart app:   $container_cmd restart $container_name"
    echo "  Remove app:    $container_cmd rm -f $container_name"
    echo
    echo "Application should be accessible at: http://localhost:$port"
    echo "============================================================================"
}

# Main execution
main() {
    echo "============================================================================"
    log_info "KPI Click Server Deployment Script"
    echo "============================================================================"
    
    # Detect container runtime
    CONTAINER_CMD=$(detect_container_runtime)
    log_info "Using container runtime: $CONTAINER_CMD"
    
    # Determine tar file
    if [ -z "$TAR_FILE" ]; then
        TAR_FILE=$(auto_detect_tar_file)
        log_info "Auto-detected tar file: $TAR_FILE"
    fi
    
    log_info "Using tar file: $TAR_FILE"
    
    # Validate inputs
    validate_tar_file "$TAR_FILE"
    
    # Load container image
    load_container_image "$TAR_FILE" "$CONTAINER_CMD"
    
    # Show completion message
    echo
    echo "============================================================================"
    log_success "Image Loading Completed Successfully!"
    echo "============================================================================"
    echo "Image: $IMAGE_NAME:latest"
    echo
    echo "To start the container manually, run:"
    echo "  $CONTAINER_CMD run -d --name $CONTAINER_NAME -p $PORT:8000 --env-file /scriptfolder/kpi_click/.env $IMAGE_NAME:latest"
    echo
    echo "Useful Commands:"
    echo "  List images:   $CONTAINER_CMD images"
    echo "  Start app:     $CONTAINER_CMD run -d --name $CONTAINER_NAME -p $PORT:8000 $IMAGE_NAME:latest"
    echo "  View logs:     $CONTAINER_CMD logs $CONTAINER_NAME"
    echo "============================================================================"
}

# Trap to handle script interruption
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"