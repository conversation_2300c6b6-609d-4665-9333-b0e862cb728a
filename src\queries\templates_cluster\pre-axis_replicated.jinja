{# Template for creating replicated table for pre-axis data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.pre_axis_{{ table_suffix }}_replicated
(
    hhkey UInt64,
    id_trip String,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number String,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact not in ["volume_loyalty_base", "population", "value_loyalty_base", "weight_wave", "BUF", ""] %}
            {{ fact }} Float64,
        {% endif %}
    {% endfor %}
    projectc Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (hhkey, id_trip)
SETTINGS index_granularity = 8192
