"""
Storage Service for KPI Results.

This module implements the primary storage service that manages different storage processors
for transferring KPI results from temporary tables to permanent storage using a modular architecture.

Key Features:
- Modular processor-based architecture for different storage types
- Intelligent routing based on storage mode configuration
- Support for local, cluster, and distributed storage modes
- Comprehensive error handling and validation
- Optimized performance for each storage type
"""

import logging
from typing import Any, Dict, List, Optional

from src.core.config import config
from src.core.clickhouse_connect import ClickHouseConnection
from src.models.axis import Period
from src.models.kpi import JobParameters
from src.services.base_service import BaseService
from src.processors.storage import (
    LocalStorageProcessor,
    ClusterStorageProcessor,
    DistributedStorageProcessor,
    MetadataStorageProcessor,
)
from src.utils.data_processing import generate_result_id


class StorageService(BaseService):
    """
    Primary service for managing KPI result storage operations.

    This service coordinates specialized storage processors to handle different aspects
    of data storage in ClickHouse environments. It provides intelligent routing,
    comprehensive error handling, and optimized performance for each storage type.

    Storage Modes:
    - Local: Store results locally using LocalStorageProcessor
    - Cluster: Store results on cluster using ClusterStorageProcessor  
    - Distributed: Create distributed tables using DistributedStorageProcessor
    - Metadata: Manage job metadata using MetadataStorageProcessor

    Architecture:
    - Processor-based modular design
    - Clear separation of concerns
    - Extensible for future storage types
    - Comprehensive validation and error handling
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        job_parameters: Optional[JobParameters] = None,
        msg_logger_func=None,
        progress_tracker=None,
        cluster_connection: Optional[ClickHouseConnection] = None,
        cluster_mode: bool = False,
        cluster_storage_mode: bool = False,
    ):
        """
        Initialize the storage service.

        Args:
            connection: ClickHouse connection instance
            job_parameters: JobParameters model containing all validated data
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
            cluster_connection: Optional ClickHouse cluster connection for distributed tables
            cluster_mode: Whether to use cluster-based distributed storage
            cluster_storage_mode: Whether to store KPI results in cluster's job_result database
        """
        super().__init__(msg_logger_func)
        self.connection = connection
        self.cluster_connection = cluster_connection
        self.cluster_mode = cluster_mode
        self.cluster_storage_mode = cluster_storage_mode
        self.job_parameters = job_parameters
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(__name__)

        # Validate cluster storage mode configuration
        if self.cluster_storage_mode and not self.cluster_connection:
            raise ValueError("cluster_storage_mode requires a valid cluster_connection")

        # Initialize storage processors
        self._initialize_processors()

        # Log storage mode configuration
        self._log_storage_configuration()

        # Track incremental updates
        self.result_id = None
        self.accumulated_query_ids = []

    def _initialize_processors(self):
        """Initialize all storage processors."""
        # Local storage processor (always available)
        self.local_processor = LocalStorageProcessor(
            self.connection, self.msg_logger_func, self.progress_tracker
        )

        # Cluster storage processor (if cluster connection available)
        self.cluster_processor = None
        if self.cluster_connection:
            self.cluster_processor = ClusterStorageProcessor(
                self.connection, self.cluster_connection, self.msg_logger_func, self.progress_tracker
            )

        # Distributed storage processor (if cluster mode enabled)
        self.distributed_processor = None
        if self.cluster_mode and self.cluster_connection:
            self.distributed_processor = DistributedStorageProcessor(
                self.connection, self.cluster_connection, self.msg_logger_func, self.progress_tracker
            )

        # Metadata storage processor (always available)
        self.metadata_processor = MetadataStorageProcessor(
            self.connection, self.msg_logger_func, self.progress_tracker
        )

    def _log_storage_configuration(self):
        """Log the current storage configuration."""
        if self.cluster_storage_mode:
            self.logger.info(
                "Cluster storage mode enabled - KPI results will be stored in cluster's job_result database"
            )
        elif self.cluster_mode:
            self.logger.info(
                "Cluster mode enabled - distributed tables will be created alongside local storage"
            )
        else:
            self.logger.info("Local storage mode - results will be stored locally")

    def get_storage_mode_info(self) -> Dict[str, Any]:
        """
        Get information about the current storage mode configuration.

        Returns:
            Dictionary containing storage mode information
        """
        return {
            "cluster_mode": self.cluster_mode,
            "cluster_storage_mode": self.cluster_storage_mode,
            "has_cluster_connection": self.cluster_connection is not None,
            "storage_location": "cluster" if self.cluster_storage_mode else "local",
            "description": self._get_storage_mode_description(),
            "processors": {
                "local": self.local_processor is not None,
                "cluster": self.cluster_processor is not None,
                "distributed": self.distributed_processor is not None,
                "metadata": self.metadata_processor is not None,
            }
        }

    def _get_storage_mode_description(self) -> str:
        """Get a human-readable description of the current storage mode."""
        if self.cluster_storage_mode:
            return "KPI results are stored in the cluster's job_result database. Metadata remains on the single-node server."
        elif self.cluster_mode:
            return "KPI results are stored locally with distributed tables created on the cluster for high availability."
        else:
            return "KPI results are stored locally on the single-node server."

    def initialize_job_metadata(
        self,
        job_parameters: JobParameters,
        username: str = "default_user",
        **kwargs,
    ) -> str:
        """
        Initialize job metadata with 'in_progress' status.

        Args:
            job_parameters: JobParameters model containing all validated data
            username: Username for the job
            **kwargs: Additional job parameters

        Returns:
            Combined result ID for the job
        """
        result_id = generate_result_id(
            job_id=job_parameters.job_id,
            analysis_name=job_parameters.analysis_name,
            period_name=",".join([p.label for p in job_parameters.periods]),
            kpi_type=job_parameters.kpi_type,
        )

        self.logger.info(f"Initializing job metadata for job {job_parameters.job_id} with result_id {result_id}")

        # Add storage configuration to kwargs
        kwargs.update({
            "backend_version": config.version,
            "cluster_mode": self.cluster_mode,
            "cluster_storage_mode": self.cluster_storage_mode,
            "storage_location": "cluster" if self.cluster_storage_mode else "local",
        })

        # Initialize metadata using metadata processor
        success = self.metadata_processor.initialize_job_metadata(
            job_parameters, username, result_id, **kwargs
        )

        if not success:
            raise RuntimeError(f"Failed to initialize job metadata for result_id {result_id}")

        # Store result_id for incremental updates
        self.result_id = result_id
        return result_id

    def transfer_temp_table_to_storage(
        self,
        temp_table_name: str,
        result_id: str,
        period: Period,
        query_prefix: Optional[str] = "query_",
    ) -> Dict[str, Any]:
        """
        Transfer data from temporary table to permanent storage.

        Routes to the appropriate storage processor based on configuration.

        Args:
            temp_table_name: Name of the temporary table (without database prefix)
            result_id: Combined result ID for the job
            period: Period information
            query_prefix: Prefix for query IDs

        Returns:
            Dictionary with transfer status information
        """
        try:
            # Route to cluster storage if cluster storage mode is enabled
            if self.cluster_storage_mode and self.cluster_processor:
                self.logger.info("Routing to cluster storage processor")
                result = self.cluster_processor.transfer_to_cluster_storage(
                    temp_table_name, result_id, period, query_prefix
                )
            else:
                # Use local storage processor
                self.logger.info("Routing to local storage processor")
                result = self.local_processor.transfer_temp_to_permanent(
                    temp_table_name, result_id, period, query_prefix
                )

                # If cluster mode is enabled, also create distributed table
                if self.cluster_mode and self.distributed_processor and result.get("success"):
                    permanent_table = result.get("permanent_table")
                    if permanent_table:
                        # Get table structure for distributed table creation
                        structure = self.local_processor.get_table_structure(permanent_table)
                        if structure:
                            distributed_table = self.distributed_processor.create_distributed_table(
                                permanent_table, structure
                            )
                            if distributed_table:
                                # Transfer data to distributed table
                                self.distributed_processor.transfer_to_distributed_table(
                                    permanent_table, query_prefix
                                )
                                result["distributed_table"] = distributed_table

            return result

        except Exception as e:
            error_msg = f"Failed to transfer temp table {temp_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def transfer_temp_table_to_storage_enhanced(
        self,
        temp_table_name: Optional[str] = None,
        result_id: str = "",
        period: Optional[Period] = None,
        query_prefix: Optional[str] = "query_",
        result_table_query: Optional[str] = None,
        result_table_structure_query: Optional[str] = None,
        direct_creation: bool = False,
        force_cluster_transfer: bool = False,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Enhanced transfer method supporting direct result table creation and various transfer modes.

        Args:
            temp_table_name: Name of the temporary table (optional for direct creation)
            result_id: Combined result ID for the job
            period: Period information
            query_prefix: Prefix for query IDs
            result_table_query: SQL query to populate the result table (for direct creation)
            result_table_structure_query: SQL query for creating table structure (for direct creation)
            direct_creation: Whether to create result table directly using queries
            force_cluster_transfer: Force cluster-to-single-node transfer mode
            **kwargs: Additional parameters

        Returns:
            Dictionary with transfer status information
        """
        try:
            # Priority 1: Direct creation mode - create distributed result table directly
            if direct_creation:
                if not result_table_query or not result_table_structure_query:
                    error_msg = "Direct creation mode requires result_table_query and result_table_structure_query"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

                if not result_id or not period:
                    error_msg = "Direct creation mode requires result_id and period"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

                if not self.distributed_processor:
                    error_msg = "Direct creation mode requires distributed processor (cluster mode)"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

                self.logger.info(f"Direct creation mode enabled, creating distributed result table for {result_id}")

                return self.distributed_processor.create_distributed_result_table(
                    result_id=result_id,
                    result_table_query=result_table_query,
                    result_table_structure_query=result_table_structure_query,
                    query_id=f"{query_prefix}direct_{self.local_processor.generate_query_id()}",
                    period=period,
                    cluster_database="job_result",
                    **kwargs,
                )

            # Validate temp_table_name for non-direct creation modes
            if not temp_table_name:
                error_msg = "temp_table_name is required when direct_creation=False"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Priority 2: Force cluster transfer - transfer from cluster to single node
            if force_cluster_transfer and self.cluster_processor:
                cluster_name = config.clickhouse_cluster_name
                database = "job_result"

                if cluster_name and database:
                    # Verify cluster table exists before attempting transfer
                    if self.cluster_processor.verify_cluster_table_exists(temp_table_name, cluster_name, database):
                        self.logger.info(f"Detected cluster table {temp_table_name}, using cluster-to-single-node transfer")
                        return self.cluster_processor.transfer_cluster_to_single_node(
                            temp_table_name, result_id, period, query_prefix, cluster_name, database
                        )
                    else:
                        self.logger.info(f"Cluster table {temp_table_name} not found, falling back to standard transfer")

            # Priority 3: Standard transfer using configured storage mode
            self.logger.info(f"Using standard transfer for {temp_table_name}")
            return self.transfer_temp_table_to_storage(temp_table_name, result_id, period, query_prefix)

        except Exception as e:
            error_msg = f"Enhanced transfer failed for {temp_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def finalize_job_storage(
        self,
        job_parameters: JobParameters,
        status: str = "done",
        error_info: Optional[Dict[str, Any]] = None,
        result_table_name: Optional[str] = None,
        **kwargs,
    ) -> bool:
        """
        Finalize job storage by updating metadata status.

        Args:
            job_parameters: JobParameters model containing all validated data
            status: Final status ('done' or 'error')
            error_info: Optional error information if status is 'error'
            result_table_name: Optional name of the result table
            **kwargs: Additional parameters

        Returns:
            True if successful, False otherwise
        """
        result_id = job_parameters.combined_result_id if job_parameters else None
        if not result_id:
            self.logger.error("No result_id provided")
            return False

        self.logger.info(
            f"Finalizing job storage for result_id {result_id} with status {status}"
        )

        # Get current job info
        job_info = self.metadata_processor._get_job_info(result_id)
        if not job_info:
            self.logger.error(f"No job info found for result_id {result_id}")
            return False

        # Update job info with final status
        # job_info["status"] = status

        if error_info:
            job_info["error"] = error_info

        # Calculate total rows if successful
        total_result_rows = job_parameters.result_rows
        final_result_table = result_table_name or ""
        if status == "done":
            job_info["total_rows"] = total_result_rows
            
        # Add axis name if json mode
        if job_parameters.json_mode:
            job_info["axes"] = {key: axis.name + " (group: " + axis.database + ", type: " + axis.type + ", author: " + axis.author + ", last update: " + axis.last_update + ")" if axis.full_name is not None else axis.axis_id for key, axis in job_parameters.axes.items()}
            job_info["filters"] = {key: filter.name + " (group: " + filter.database + ", type: " + filter.type + ", author: " + filter.author + ", last update: " + filter.last_update + ")" for key, filter in job_parameters.filters.items()}
        

        return self.metadata_processor.finalize_job_metadata(
            result_id,
            status=status,
            result_table_name=final_result_table,
            job_duration=f"{job_parameters.job_duration:.2f} ms"
            if job_parameters.job_duration
            else "",
            query_ids=", ".join([str(qid) for qid in job_parameters.query_ids])
            if job_parameters.query_ids
            else "",
            final_result_table=final_result_table,
            result_rows=total_result_rows,
            error_info=error_info,
            job_info=job_info,
            **kwargs
        )

    def update_job_metadata(self, result_id: str, **update_params: Any) -> bool:
        """
        Update job metadata with provided parameters.

        Args:
            result_id: The ID of the result to update
            **update_params: Dictionary of column names and values to update

        Returns:
            True if successful, False otherwise
        """
        return self.metadata_processor.update_job_metadata(result_id, **update_params)

    def get_job_status(self, result_id: str) -> Optional[str]:
        """
        Get the current status of a job.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job status ('in_progress', 'done', 'error') or None if not found
        """
        return self.metadata_processor.get_job_status(result_id)

    def get_job_info(self, result_id: str) -> Optional[Dict[str, Any]]:
        """
        Get job info from metadata.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job info dictionary or None if not found
        """
        return self.metadata_processor.get_job_info(result_id)

    def validate_storage(self, table_name: str) -> Dict[str, Any]:
        """
        Validate storage for a given table across all configured storage types.

        Args:
            table_name: Name of the table to validate

        Returns:
            Dictionary with validation results for each storage type
        """
        validation_results = {
            "local": None,
            "cluster": None,
            "distributed": None,
            "overall_success": False
        }

        try:
            # Validate local storage
            validation_results["local"] = self.local_processor.validate_storage(table_name)

            # Validate cluster storage if available
            if self.cluster_processor:
                validation_results["cluster"] = self.cluster_processor.cluster_table_exists(table_name)

            # Validate distributed storage if available
            if self.distributed_processor:
                distributed_table = self.distributed_processor.get_distributed_table_name(table_name)
                if distributed_table:
                    validation_results["distributed"] = self.distributed_processor.validate_distributed_storage(distributed_table)

            # Determine overall success
            local_success = validation_results["local"] and validation_results["local"].get("table_exists", False)
            
            if self.cluster_storage_mode:
                validation_results["overall_success"] = validation_results.get("cluster", False)
            else:
                validation_results["overall_success"] = local_success

            return validation_results

        except Exception as e:
            self.logger.error(f"Failed to validate storage for {table_name}: {str(e)}")
            validation_results["error"] = str(e)
            return validation_results

    def cleanup_temp_tables(self, table_names: List[str]) -> Dict[str, bool]:
        """
        Clean up temporary tables across all storage types.

        Args:
            table_names: List of table names to clean up

        Returns:
            Dictionary mapping table names to cleanup success status
        """
        cleanup_results = {}

        try:
            # Clean up local temp tables
            local_results = self.local_processor.cleanup_temp_tables(table_names)
            cleanup_results.update(local_results)

            # Clean up cluster temp tables if cluster processor is available
            if self.cluster_processor:
                # Note: Cluster cleanup should be handled by the cluster connection
                # that created the tables, but we can log the request
                for table_name in table_names:
                    self.logger.info(f"Cluster table cleanup requested for {table_name}")

            return cleanup_results

        except Exception as e:
            self.logger.error(f"Failed to cleanup temp tables: {str(e)}")
            return {table_name: False for table_name in table_names}

    def get_processor_status(self) -> Dict[str, Any]:
        """
        Get status information about all processors.

        Returns:
            Dictionary with processor status information
        """
        return {
            "local_processor": {
                "available": self.local_processor is not None,
                "connection_active": self.connection is not None,
            },
            "cluster_processor": {
                "available": self.cluster_processor is not None,
                "connection_active": self.cluster_connection is not None,
            },
            "distributed_processor": {
                "available": self.distributed_processor is not None,
                "cluster_mode": self.cluster_mode,
            },
            "metadata_processor": {
                "available": self.metadata_processor is not None,
            },
        }

    def test_connections(self) -> Dict[str, bool]:
        """
        Test all available connections.

        Returns:
            Dictionary with connection test results
        """
        results = {}

        try:
            # Test main connection
            test_query = "SELECT 1 as test"
            result = self.connection.get_query_dataframe(test_query)
            results["main_connection"] = not result.empty
        except Exception as e:
            self.logger.error(f"Main connection test failed: {str(e)}")
            results["main_connection"] = False

        try:
            # Test cluster connection if available
            if self.cluster_connection:
                result = self.cluster_connection.get_query_dataframe(test_query)
                results["cluster_connection"] = not result.empty
            else:
                results["cluster_connection"] = None
        except Exception as e:
            self.logger.error(f"Cluster connection test failed: {str(e)}")
            results["cluster_connection"] = False

        return results

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        Get comprehensive information about a table across all storage types.

        Args:
            table_name: Name of the table to analyze

        Returns:
            Dictionary with table information
        """
        info = {
            "table_name": table_name,
            "local": {},
            "cluster": {},
            "distributed": {},
        }

        try:
            # Local table info
            info["local"]["exists"] = self.local_processor.table_exists(table_name)
            if info["local"]["exists"]:
                info["local"]["row_count"] = self.local_processor.count_table_rows(table_name)
                info["local"]["structure"] = self.local_processor.get_table_structure(table_name)

            # Cluster table info
            if self.cluster_processor:
                info["cluster"]["exists"] = self.cluster_processor.cluster_table_exists(table_name)
                if info["cluster"]["exists"]:
                    info["cluster"]["row_count"] = self.cluster_processor.count_table_rows(
                        table_name, self.cluster_connection
                    )

            # Distributed table info
            if self.distributed_processor:
                distributed_name = self.distributed_processor.get_distributed_table_name(table_name)
                if distributed_name:
                    info["distributed"]["name"] = distributed_name
                    info["distributed"]["exists"] = self.distributed_processor.table_exists_on_cluster(
                        distributed_name
                    )

            return info

        except Exception as e:
            self.logger.error(f"Failed to get table info for {table_name}: {str(e)}")
            info["error"] = str(e)
            return info