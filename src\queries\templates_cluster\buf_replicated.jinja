{# Template for creating replicated table for pre-axis data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.buf_{{ table_suffix }}_replicated
(
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number String,
            {% endif %}
        {% endif %}
    {% endfor %}
    BUF Float64,
    rwbasis String,
    buyers_ww Float64
    {% if catman %}, value Float64 {% endif %}
    {% if ("axsh" not in axes.values()|map(attribute="type") or (filters is not none and "flth" not in filters.values()|map(attribute="type"))) and facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
        trial_ww Float64,
        repeaters_ww Float64
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["repeat_rate", "penetration_repeaters", "repeaters_000", "trial_000"])|list %},
        p_trial_trial Float64,
        p_rep_rep Float64,
        p_rep_trial Float64,
        p_trial_rep Float64
    {% endif %}
)
ENGINE = ReplicatedMergeTree()
ORDER BY (
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    rwbasis
)
SETTINGS index_granularity = 8192
