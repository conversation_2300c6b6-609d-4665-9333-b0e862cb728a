# KPI Click

A Python tool for processing and analyzing KPIs using ClickHouse database.

## Project Structure

```
kpiclick/
├── src/
│   ├── core/                     # Core functionality
│   │   ├── clickhouse_connect.py # Database connection (clickhouse-connect)
│   │   ├── clickhouse_driver.py  # Database connection (clickhouse-driver)
│   │   ├── config.py             # Configuration management
│   │   └── resource_check.py     # Resource monitoring
│   ├── processors/            # Processing logic
│   │   ├── axis_processor.py  # Axis data processing
│   │   └── query_processor.py # Query splitting and processing
│   ├── queries/               # Query building and templates
│   │   ├── query_builder.py   # SQL query building
│   │   └── templates/         # SQL templates
│   ├── utils/                 # Utility functions
│   │   └── job_api_client.py  # Job API client
│   └── models/                # Data models
│       ├── axis.py            # Axis data models
│       └── kpi.py             # KPI data models
├── tests/                     # Unit tests
├── examples/                  # Example usage
├── main.py                    # Entry point
├── pyproject.toml             # Project configuration
├── .env                       # Environment variables
└── README.md                  # Project documentation
```

## Requirements

- Python 3.12+
- Poetry for dependency management

## Installation

1. Clone the repository
2. Install dependencies using Poetry:
```bash
poetry install
```

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# ClickHouse configuration
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=default

# PostgreSQL configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_APP_DB=postgres
POSTGRES_APP_USER=postgres
POSTGRES_APP_PWD=postgres

# Magic Gateway configuration
MG_API_URL=http://localhost:8000
MG_API_USER=user
MG_API_PWD=password

# Job API configuration (only for retrieving job information)
JOB_API_URL=http://localhost:8001

# Application configuration
OUTPUT_DIR=/reportoutput/kpi_click
MEMLOG_FOLDER=memlog
```

## Usage

Run the application with the following command:

```bash
python main.py <id_job> <username> <output_dir>
```

Where:
- `id_job`: Job ID to process
- `username`: Username for logging
- `output_dir`: Output directory for results (optional, defaults to config value)

The results will be exported to the specified output directory in your specified format (Excel by default).

## License

Private



## Deployment

The KPI Click application uses Docker containers for deployment. The deployment process consists of two main steps: building and preparing the container image on a development machine, then deploying it to the target server.

### Prerequisites

- **Development Machine**: Docker or Podman installed
- **Target Server**: Docker installed
- **Network Access**: GitLab repository access for private dependencies

### Step 1: Build and Prepare (Development Machine)

#### Windows Development Environment

Use the provided batch script to build the Docker image and prepare it for deployment:

```batch
# Build with default settings
deploy-prepare.bat

# Build with custom parameters
deploy-prepare.bat [tag] [project_id] [job_token]
```

The script will:
- Build the Docker image with GitLab repository access
- Save the image as a tar file in the `deployment/` directory
- Create deployment information file
- Asks if you want to open the deployment folder


### Step 2: Deploy to Server (Linux Server)

#### Transfer Files

Copy the generated tar file and deployment script to your target server:

# Example for ***********:
copy localhost_kpi_click-app-latest-*.tar deploy-run.sh to /scriptfolder/kpi_click/


#### Server Environment Setup

Ensure the environment file exists at `/scriptfolder/kpi_click/.env`:

```bash
# Create or edit environment file
sudo nano /scriptfolder/kpi_click/.env
```

#### Deploy the Application

Run the deployment script on the target server:

```bash
# Make script executable
chmod +x deploy-run.sh

# Deploy with auto-detected tar file
./deploy-run.sh

# Deploy with specific tar file
./deploy-run.sh localhost_kpi_click-app-latest-20250810_000206.tar

# Deploy with custom container name and port
./deploy-run.sh app.tar kpiclick-prod 9000
```

The deployment script will:
- Auto-detect the latest tar file if not specified
- Remove any existing containers and images
- Load the new Docker image
- Provide commands for manual container startup

### Deployment Scripts Reference

#### deploy-prepare.bat (Windows)

**Purpose**: Build Docker image and prepare for deployment

**Usage**: `deploy-prepare.bat [tag] [project_id] [job_token]`

**Features**:
- Builds Docker image with GitLab repository access
- Creates timestamped tar file in `deployment/` directory
- Generates deployment information file
- Opens deployment folder automatically
- Handles Windows path issues with image names

#### deploy-run.sh (Linux)

**Purpose**: Deploy and load Docker image on target server

**Usage**: `./deploy-run.sh [tar_file] [container_name] [port]`

**Features**:
- Auto-detects latest tar file in current directory
- Validates tar file integrity
- Removes existing containers and images cleanly
- Loads fresh Docker image
- Provides manual startup commands
- Uses environment file from `/scriptfolder/kpi_click/.env`

### Environment Variables

The deployment scripts support the following environment variables:

- `KPI_CLICK_ENV_FILE`: Custom path to environment file (default: `/scriptfolder/kpi_click/.env`)
- `KPI_CLICK_DATA_DIR`: Custom data directory for volume mounts

#### Useful Commands

```bash
# Check Docker images
docker images

# View container logs
docker logs kpi_click

# Check container status
docker ps -a

# Remove all stopped containers
docker container prune

# Remove unused images
docker image prune
```
