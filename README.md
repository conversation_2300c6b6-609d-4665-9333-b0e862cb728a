# KPI Click

A Python tool for processing and analyzing KPIs using ClickHouse database.

## Project Structure

```
kpiclick/
├── src/
│   ├── core/                     # Core functionality
│   │   ├── clickhouse_connect.py # Database connection (clickhouse-connect)
│   │   ├── clickhouse_driver.py  # Database connection (clickhouse-driver)
│   │   ├── config.py             # Configuration management
│   │   └── resource_check.py     # Resource monitoring
│   ├── processors/            # Processing logic
│   │   ├── axis_processor.py  # Axis data processing
│   │   └── query_processor.py # Query splitting and processing
│   ├── queries/               # Query building and templates
│   │   ├── query_builder.py   # SQL query building
│   │   └── templates/         # SQL templates
│   ├── utils/                 # Utility functions
│   │   └── job_api_client.py  # Job API client
│   └── models/                # Data models
│       ├── axis.py            # Axis data models
│       └── kpi.py             # KPI data models
├── tests/                     # Unit tests
├── examples/                  # Example usage
├── main.py                    # Entry point
├── pyproject.toml             # Project configuration
├── .env                       # Environment variables
└── README.md                  # Project documentation
```

## Requirements

- Python 3.12+
- Poetry for dependency management

## Installation

1. Clone the repository
2. Install dependencies using Poetry:
```bash
poetry install
```

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# ClickHouse configuration
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=default

# PostgreSQL configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_APP_DB=postgres
POSTGRES_APP_USER=postgres
POSTGRES_APP_PWD=postgres

# Magic Gateway configuration
MG_API_URL=http://localhost:8000
MG_API_USER=user
MG_API_PWD=password

# Job API configuration (only for retrieving job information)
JOB_API_URL=http://localhost:8001

# Application configuration
OUTPUT_DIR=/reportoutput/kpi_click
MEMLOG_FOLDER=memlog
```

## Usage

Run the application with the following command:

```bash
python main.py <id_job> <username> <output_dir>
```

Where:
- `id_job`: Job ID to process
- `username`: Username for logging
- `output_dir`: Output directory for results (optional, defaults to config value)

The results will be exported to the specified output directory in your specified format (Excel by default).

## License

Private



## Deployment

### Building with Private GitLab Repository Access

To build the image with access to private GitLab repositories, use the provided build scripts:

#### Using Bash (Linux/macOS):

```bash
# Make the script executable
chmod +x build-podman.sh

# Build with GitLab credentials
./build-podman.sh --project-id YOUR_PROJECT_ID --job-token YOUR_JOB_TOKEN
```

#### Using PowerShell (Windows):

```powershell
# Build with GitLab credentials
.\build-podman.ps1 -ProjectId YOUR_PROJECT_ID -JobToken YOUR_JOB_TOKEN
```

### Standard Build (without private repo access)

```bash
podman build -t kpi_click:latest .
podman save kpi_click:latest -o ../kpi_click.tar
```

### Deployment

#### On DEV environment (***********):
```bash
sudo docker load < ~/kpi_click.tar
```

#### On PROD environment (***********):
```bash
docker load < ~/kpi_click.tar
```
