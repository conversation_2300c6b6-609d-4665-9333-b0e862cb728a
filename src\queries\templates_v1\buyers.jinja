{# Calculation of buyers #}
SELECT
    hhkey,
    rwbasis,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact == "weight_wave" %}
            any({{ fact }}) AS buyers_ww,
        {% endif %}
        {% if fact == "BUF" %}
            count(*) as trips_raw,
            sum(weight_wave) AS trips_ww,
            any(population) AS population,
        {% endif %}
        {% if fact == "rw_compensat" %}
            sum(rw_compensat * fullmass) AS trips_fullmass,
        {% endif %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            sum({{ fact }}) AS {{ fact }},
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "trips_raw")|list and  "BUF" not in required_facts %}
        count(*) as trips_raw,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["loyalty_volume", "loyalty_base_volume"])|list %}
        any(volume_loyalty_base) AS volume_loyalty_base,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["loyalty_value", "loyalty_base_value"])|list %}
        any(value_loyalty_base) AS value_loyalty_base,
    {% endif %}
    {% if id_panel == 3 %}
        avg(projectc) as projectc
    {% else %}
        any(projectc) as projectc
    {% endif %}
FROM axis
{% if facts_axis|selectattr("code_name", "in", ["loyalty_value", "loyalty_volume", "loyalty_base_value", "loyalty_base_volume"])|list %}
    LEFT JOIN (
    SELECT
    {% set ns = namespace(loyalty_fact=none) %}
    {% for fact in facts_axis %}
        {% if fact["code_name"] in ["loyalty_volume", "loyalty_value"] %}
            sum({{ fact["code_name"][8:] }}_rp) AS {{ fact["code_name"][8:] }}_loyalty_base,
            {% set ns.loyalty_fact = fact %}
        {% elif fact["code_name"] == "loyalty_base_value" and facts_axis|selectattr("code_name", "equal_to", "loyalty_value") %}
            sum({{ fact["code_name"][13:] }}_rp) AS {{ fact["code_name"][13:] }}_loyalty_base,
            {% set ns.loyalty_fact = fact %}
        {% elif fact["code_name"] == "loyalty_base_volume" and facts_axis|selectattr("code_name", "equal_to", "loyalty_volume") %}
            sum({{ fact["code_name"][13:] }}_rp) AS {{ fact["code_name"][13:] }}_loyalty_base,
            {% set ns.loyalty_fact = fact %}
        {% endif %}
    {% endfor %}
    {% for axis_key, axis_data in axes.items() %}
        {% if (axis_data["type"] is not none) and (axis_key != ns.loyalty_fact["relative_axis"]) %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    hhkey
    FROM axis
        WHERE
        {% if ns.loyalty_fact is not none and ns.loyalty_fact["relative_axis"] is not none %}
            {{ ns.loyalty_fact["relative_axis"] }}_position_number = '{{ ns.loyalty_fact["relative_position"] }}|'
        {% else %}
            first_axis_position_number = '1|'
        {% endif %}
    GROUP BY hhkey
    {% for axis_key, axis_data in axes.items() %}
        {% if (axis_data["type"] is not none) %}
            {% if axis_data["type"] != "axsh" %}
                , {{ axis_key }}_position_number
            {% endif %}
        {% endif %}
    {% endfor %}
    ) AS loyalty_base USING (hhkey
        {% for axis_key, axis_data in axes.items() %}
            {% if (axis_data["type"] is not none) and (axis_key != ns.loyalty_fact["relative_axis"]) %}
                {% if axis_data["type"] != "axsh" %}
                    , {{ axis_key }}_position_number
                {% endif %}
            {% endif %}
        {% endfor %}
        )
{% endif %}
GROUP BY hhkey, rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
