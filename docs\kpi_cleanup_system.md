# KPI Results Cleanup System

This document describes the automated cleanup system for KPI result tables and metadata management.

## Overview

The KPI cleanup system consists of two main components:

1. **SQL DDL Migration**: Adds lifecycle status tracking to the metadata table
2. **Airflow DAG**: Implements automated cleanup logic with safety checks

## Table Naming Conventions

Based on the codebase analysis, result tables follow this naming pattern:

- **Result Table Names**: `job_{job_id}_{hash}` (e.g., `job_12345_a1b2c3d4`)
- **Database Schema**: Tables are stored in various databases:
  - Local mode: Default database or `kpi_results`
  - Cluster mode: `job_result` database
  - Distributed tables: `{table_name}_distributed`

## Multi-Storage Support

The cleanup system now supports multiple ClickHouse storage configurations:

### Storage Types

1. **Standard Storage**: Uses storage connection for regular tables
   - Database: Any database except `kpi_result` with `_distributed` suffix
   - Connection: Storage connection (clickhouse-driver or clickhouse-connect)
   - DROP syntax: `DROP TABLE IF EXISTS {table_name}`

2. **Cluster Storage**: Uses cluster connection for distributed tables
   - Database: `kpi_result` database with tables ending in `_distributed`
   - Connection: Cluster connection with distributed table support
   - DROP syntax: `DROP TABLE IF EXISTS {table_name} SYNC`

### Multi-Storage Cleanup Manager

The `MultiStorageCleanupManager` automatically:
- Determines appropriate connection type based on table name and database
- Uses correct DROP TABLE syntax for each storage type
- Handles both replicated and distributed table cleanup
- Provides batch processing capabilities
- Tracks storage type usage statistics

## Lifecycle Status Values

The new `lifecycle_status` column supports these values:

| Status | Value | Description |
|--------|-------|-------------|
| `ACTIVE` | 1 | Current/valid result, actively used |
| `OUTDATED` | 2 | Superseded by newer results from reruns |
| `EXPIRED` | 3 | Exceeded retention period, marked for deletion |
| `DELETED` | 4 | Result tables have been deleted |

## Migration Process

### Step 1: Apply SQL DDL Migration

```python
# Execute the migration script
import os
from clickhouse_driver import Client

# Initialize ClickHouse client
client = Client('localhost')

# Read the migration SQL file
sql_file_path = 'src/sql/metadata_lifecycle_status_migration.sql'
with open(sql_file_path, 'r') as f:
    migration_sql = f.read()

# Execute the migration
client.execute(migration_sql)
```

The migration script:
- Creates a new table with enhanced schema
- Migrates existing data with appropriate initial status
- Creates performance indexes
- Provides backward compatibility view

### Step 2: Deploy Airflow DAG

1. Copy the DAG file to your Airflow DAGs directory:
```bash
cp airflow/dags/kpi_results_cleanup_dag.py /path/to/airflow/dags/
```

2. Configure Airflow Variables:
```bash
# Set cleanup configuration
airflow variables set kpi_cleanup_dry_run true
airflow variables set kpi_cleanup_max_tables 50
airflow variables set kpi_cleanup_safety_check true
airflow variables set kpi_cleanup_retention_buffer 7
airflow variables set kpi_cleanup_stuck_job_timeout_hours 3
airflow variables set kpi_clickhouse_conn_id clickhouse_default
airflow variables set kpi_cleanup_notification_email <EMAIL>
```

3. Configure ClickHouse connection in Airflow UI:
   - Connection ID: `clickhouse_default`
   - Connection Type: `Postgres` (or custom ClickHouse type)
   - Host: Your ClickHouse server
   - Port: 9000 (or your ClickHouse port)
   - Schema: `default`
   - Login/Password: Your credentials

## Configuration Options

### Airflow Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `kpi_cleanup_dry_run` | `true` | Enable dry run mode (no actual deletions) |
| `kpi_cleanup_max_tables` | `50` | Maximum tables to process per run |
| `kpi_cleanup_safety_check` | `true` | Enable safety checks |
| `kpi_cleanup_retention_buffer` | `7` | Extra days before marking as expired |
| `kpi_cleanup_stuck_job_timeout_hours` | `3` | Hours after which in_progress jobs are considered stuck |
| `kpi_clickhouse_conn_id` | `clickhouse_default` | Airflow connection ID |
| `kpi_cleanup_notification_email` | `<EMAIL>` | Email for notifications |

### Safety Checks

The system includes multiple safety mechanisms:

1. **Maximum Tables Limit**: Prevents processing too many tables at once
2. **Percentage Check**: Won't delete more than 50% of active jobs
3. **Dry Run Mode**: Test operations without actual deletions
4. **Retention Buffer**: Extra days before marking jobs as expired
5. **Table Existence Check**: Verifies tables exist before dropping

## Cleanup Logic

### Identifying Expired Jobs

Jobs are marked as `EXPIRED` when:

**Retention Period Exceeded:**
- `lifecycle_status = 'ACTIVE'`
- `status = 'done'`
- `created_at < (now() - retention_days - buffer_days)`

**Stuck in Progress:**
- `lifecycle_status = 'ACTIVE'`
- `status = 'in_progress'`
- `created_at < (now() - stuck_job_timeout_hours)`

The system automatically identifies and cleans up jobs that have been stuck in 'in_progress' status for more than the configured timeout period (default: 3 hours). This prevents accumulation of orphaned jobs that may have failed to update their status due to system crashes, network issues, or other failures.

### Identifying Superseded Jobs

Jobs are marked as `OUTDATED` when newer jobs with identical analysis parameters exist. The superseding logic operates in two scenarios:

#### Scenario 1: Cleanup Operations (Batch Processing)
During scheduled cleanup runs (Airflow DAG or manual cleanup), the system:
- Identifies jobs with identical parameters: `analysis_name`, `kpi_type`, `id_panel`, `username`, `periods`
- Marks older jobs as `lifecycle_status = 'OUTDATED'`
- Populates `superseded_by` column with the ID of the newer superseding job
- Sets `cleanup_reason = 'superseded_by_newer_result'`
- Sets `marked_for_deletion_at = now()`

#### Scenario 2: Real-time Superseding (Job Completion)
When a job completes successfully (`status = 'done'`), the system automatically:
- Checks for existing ACTIVE jobs with identical analysis parameters
- Immediately marks older jobs as `lifecycle_status = 'OUTDATED'`
- Populates `superseded_by` column with the new job's ID
- Provides real-time superseding without waiting for cleanup runs

#### Superseding Criteria
Jobs are superseded when ALL of the following parameters match exactly:
- `analysis_name`: Type of analysis (e.g., "buyers_catman", "penetration_analysis")
- `kpi_type`: KPI calculation type (e.g., "penetration", "frequency", "volume")
- `id_panel`: Panel identifier (numeric)
- `username`: User who initiated the analysis
- `periods`: Time periods analyzed (comma-separated string)

#### Additional Requirements
- Jobs must have `status = 'done'` (completed successfully)
- Jobs must have `lifecycle_status = 'ACTIVE'` (not already processed)
- Jobs must have `final_result_table != ''` (valid result table)
- Only older jobs are superseded (based on `created_at` timestamp)

#### Superseded_by Column Tracking

The `superseded_by` column provides complete audit trail for superseded jobs:

**Column Purpose:**
- Stores the ID of the job that superseded the current job
- Only populated when `lifecycle_status = 'OUTDATED'`
- Enables tracking of superseding relationships for audit and debugging

**Population Logic:**
```sql
-- Example: Job A superseded by Job B
UPDATE metadata.results_metadata
SET
    lifecycle_status = 'OUTDATED',
    superseded_by = 'job_b_result_id',
    marked_for_deletion_at = now(),
    cleanup_reason = 'superseded_by_newer_result'
WHERE id = 'job_a_result_id'
```

**Query Examples:**
```sql
-- Find all jobs superseded by a specific job
SELECT * FROM metadata.results_metadata
WHERE superseded_by = 'specific_job_id';

-- Find superseding chains
SELECT
    id as superseded_job,
    superseded_by as superseding_job,
    created_at,
    analysis_name
FROM metadata.results_metadata
WHERE lifecycle_status = 'OUTDATED'
  AND superseded_by != ''
ORDER BY analysis_name, created_at;
```

### Table Cleanup Process

1. Query jobs marked as `EXPIRED` or `OUTDATED`
2. Check if result tables exist
3. Drop tables using appropriate storage connection and syntax
4. Update metadata with `deleted_at` timestamp
5. Set `lifecycle_status = 'DELETED'`

The enhanced cleanup process now supports:
- **Multi-storage detection**: Automatically determines storage type based on table name
- **Appropriate DROP syntax**: Uses `SYNC` for distributed tables, standard syntax for others
- **Batch processing**: Processes multiple tables efficiently
- **Storage type tracking**: Reports breakdown of cluster vs storage operations

## Legacy Migration

### Legacy Cleanup Script

The `scripts/migrate_legacy_cleanup.py` script handles migration from the old metadata system:

```bash
# List legacy tables (dry run)
python scripts/migrate_legacy_cleanup.py --list-tables --dry-run

# Clean up all legacy tables (dry run)
python scripts/migrate_legacy_cleanup.py --cleanup --dry-run

# Clean up specific pattern of tables
python scripts/migrate_legacy_cleanup.py --cleanup --table-pattern "job_2024*" --max-tables 100

# Perform actual cleanup
python scripts/migrate_legacy_cleanup.py --cleanup --max-tables 50
```

### Legacy Migration Features

- **Legacy Metadata Access**: Connects to `kpi_results.results` table
- **Multi-storage Support**: Uses same multi-storage cleanup as main system
- **Pattern Filtering**: Supports SQL LIKE patterns for selective cleanup
- **Batch Processing**: Efficient cleanup of multiple legacy tables
- **Safety Limits**: Configurable maximum table limits
- **Comprehensive Logging**: Detailed operation logs and reports

### Migration Process

1. **Assessment**: List all legacy tables to understand scope
2. **Pattern-based Cleanup**: Clean up tables by date or pattern
3. **Verification**: Confirm tables are properly removed
4. **Documentation**: Generate cleanup reports for audit trail

## Monitoring and Reporting

### DAG Execution Report

Each run generates a comprehensive report including:
- Jobs identified for cleanup
- Metadata update statistics
- Table cleanup results
- Configuration used
- Success/failure details

### Monitoring Queries

```sql
-- Check cleanup status distribution
SELECT lifecycle_status, COUNT(*) as count
FROM metadata.results_metadata
GROUP BY lifecycle_status;

-- Find recently cleaned up jobs with cleanup reasons
SELECT id, job_id, lifecycle_status, cleanup_reason, marked_for_deletion_at, deleted_at
FROM metadata.results_metadata
WHERE lifecycle_status IN ('EXPIRED', 'OUTDATED', 'DELETED')
  AND marked_for_deletion_at > now() - INTERVAL 7 DAY
ORDER BY marked_for_deletion_at DESC;

-- Check for stuck in-progress jobs
SELECT id, job_id, analysis_name, created_at, status,
       dateDiff('hour', created_at, now()) as hours_running
FROM metadata.results_metadata
WHERE status = 'in_progress'
  AND lifecycle_status = 'ACTIVE'
  AND created_at < (now() - toIntervalHour(3))
ORDER BY created_at;

-- Check retention compliance
SELECT
    COUNT(*) as total_jobs,
    SUM(CASE WHEN created_at < (now() - toIntervalDay(retention_days)) THEN 1 ELSE 0 END) as should_be_expired,
    SUM(CASE WHEN lifecycle_status = 'EXPIRED' THEN 1 ELSE 0 END) as marked_expired
FROM metadata.results_metadata
WHERE status = 'done';

-- Cleanup reason breakdown
SELECT cleanup_reason, COUNT(*) as count
FROM metadata.results_metadata
WHERE lifecycle_status IN ('EXPIRED', 'DELETED')
  AND cleanup_reason != ''
GROUP BY cleanup_reason
ORDER BY count DESC;
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify ClickHouse connection configuration
   - Check network connectivity
   - Validate credentials

2. **Permission Errors**
   - Ensure Airflow has DROP TABLE permissions
   - Verify ALTER TABLE permissions for metadata updates

3. **Safety Check Failures**
   - Review safety check thresholds
   - Check if too many jobs are being identified
   - Verify active job counts

4. **Table Not Found Errors**
   - Normal for tables already manually deleted
   - System will mark as deleted anyway
   - Check table naming conventions

### Debug Mode

Enable debug logging by setting Airflow task log level to DEBUG:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Best Practices

1. **Start with Dry Run**: Always test with `dry_run=true` first
2. **Monitor Regularly**: Check cleanup reports and metrics
3. **Adjust Retention**: Set appropriate retention periods per use case
4. **Backup Strategy**: Consider backing up important results before cleanup
5. **Gradual Rollout**: Start with small `max_tables_per_run` values
6. **Schedule Appropriately**: Run during low-usage periods

## Dependencies

- Apache Airflow 2.0+
- ClickHouse database
- Python packages: `clickhouse-driver`, `pandas`
- Airflow providers: `apache-airflow-providers-postgres`

## Security Considerations

- Use dedicated service account for cleanup operations
- Limit permissions to only necessary databases/tables
- Enable audit logging for all cleanup operations
- Implement approval workflows for production environments
- Regular backup verification before cleanup runs
