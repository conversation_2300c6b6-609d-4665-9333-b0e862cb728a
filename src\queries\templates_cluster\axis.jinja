SELECT
    hhkey,
    id_trip,
    rwbasis,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact == "rw_compensat"  %}
            avg({{ fact }}) AS {{ fact }},
        {% endif %}
        {% if fact in ("weight_wave", "fullmass")  %}
            any({{ fact }}) AS {{ fact }},
        {% endif %}
        {% if fact == "BUF" %}
            any(population) AS population,
        {% endif %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            sum({{ fact }}) AS {{ fact }},
        {% endif %}
    {% endfor %}
    {% if id_panel == 3 %}
        avg(projectc) as projectc
    {% else %}
        any(projectc) as projectc
    {% endif %}
FROM pre_axis_{{ table_suffix }}_distributed
GLOBAL INNER JOIN (
    SELECT
    {% if "weight_wave" in required_facts or "BUF" in required_facts %}
        sum(fullmass) / (dateDiff('month', toDate('{{ period_start }}'), toDate('{{ period_end }}')) + 1) AS weight_wave,
    {% endif %}
    {% if "BUF" in required_facts %}
        sum(weight_wave) OVER (PARTITION BY rwbasis) AS population,
    {% endif %}
    hhkey,
    argMax(rwbasis, dt_start) AS rwbasis
    FROM pet.hh_weights_fullmass_dist
    WHERE (id_panel={{ id_panel }}) AND (dt_start >= '{{ period_start }}') AND (dt_end <= '{{ period_end }}')
    GROUP BY hhkey
) p USING (hhkey)
GROUP BY hhkey, rwbasis, id_trip
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
ORDER BY hhkey, rwbasis, id_trip
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
