{# Calculation of buyers anywhere #}
WITH buyers AS (
SELECT
    hhkey,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" and axis_key != "third_axis" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    {# {% for fact in required_facts %}
        {% if fact == "weight_wave" %}
            any({{ fact }}) AS buyers_anywhere_ww,
        {% endif %}
        {% if fact == "rw_compensat"%}
            sum(rw_compensat * fullmass) AS trips_anywhere_fm,
        {% endif %}
        {% if fact == "value_rp" %}
            sum({{ fact }}) AS value_buyers_anywhere,
        {% endif %}
        {% if fact == "number_rp" %}
            sum({{ fact }}) AS packs_buyers_anywhere,
        {% endif %}
    {% endfor %} #}
    any(weight_wave) AS buyers_anywhere_ww,
    sum(weight_wave) AS trips_anywhere_ww,
    sum(rw_compensat * fullmass) AS trips_anywhere_fm,
    sum(value_rp) AS value_buyers_anywhere
    {# sum(number_rp) AS packs_buyers_anywhere #}
FROM axis
WHERE
    third_axis_position_number = 1
GROUP BY hhkey
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" and axis_key != "third_axis" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
),
{# Calculation of shoppers anywhere #}
shoppers AS (
SELECT
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_key == "third_axis" %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    any(projectc) as projectc,
    hhkey
FROM axis p
WHERE
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" and axis_key != "third_axis" %}
            {{ axis_key }}_position_number = 1 AND
        {% endif %}
    {% endif %}
{% endfor %}
TRUE
GROUP BY hhkey
{% for axis_key, axis_data in axes.items() %}
    {% if axis_key == "third_axis" %}
        , {{ axis_key }}_position_number
    {% endif %}
{% endfor %}
),
{# Purchases anywhere by shoppers #}
shoppers_purchases AS (
SELECT
    *
FROM shoppers
LEFT JOIN buyers USING (hhkey)
),
{# Calculation of buyers in shop #}
shop_buyers AS (
SELECT
    hhkey,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    {# {% for fact in required_facts %}
        {% if fact == 'weight_wave' %}
            any({{ fact }}) AS buyers_ww,
            any(buyers_anywhere_ww) AS buyers_elsewhere_ww,
        {% endif %}
        {% if fact == 'BUF' %}
            count(*) as trips_raw,
            sum(weight_wave) AS trips_ww,
            any(population) AS population,
            any(trips_anywhere_fm) AS trips_elsewhere_fm,
        {% endif %}
        {% if fact == "rw_compensat" %}
            sum(rw_compensat * fullmass) AS trips_fullmass,
        {% endif %}
        {% if fact in ("value_rp", "number_rp") %}
            sum({{ fact }}) AS {{ fact }},
            {% if fact == "value_rp" %}
                any(value_buyers_anywhere) AS value_buyers_elsewhere,
            {% elif fact == "number_rp" %}
                any(packs_buyers_anywhere) AS packs_buyers_elsewhere,
            {% endif %}
        {% endif %}
    {% endfor %} #}
    count(*) as trips_raw,
    any(weight_wave) AS buyers_ww,
    sum(weight_wave) AS trips_ww,
    sum(rw_compensat * fullmass) AS trips_fullmass,
    sum(value_rp) AS value_rp,
    AVG(buyers_anywhere_ww) AS buyers_elsewhere_ww,
    AVG(trips_anywhere_ww) AS trips_elsewhere_ww,
    AVG(trips_anywhere_fm) AS trips_elsewhere_fm,
    AVG(value_buyers_anywhere) AS value_buyers_elsewhere
FROM axis a
INNER JOIN shoppers_purchases b USING (
hhkey
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
)
GROUP BY hhkey
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
)
{# Combining buyers in shop with shoppers anywhere #}
SELECT
    *
FROM shop_buyers b
FULL JOIN shoppers_purchases a USING (
hhkey
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
)
INNER JOIN (
    SELECT
        {% if "weight_wave" in required_facts or "BUF" in required_facts %}
            sum(fullmass) / (dateDiff('month', toDate('{{ period_start }}'), toDate('{{ period_end }}')) + 1) AS weight_wave,
        {% endif %}
        {% if "BUF" in required_facts %}
            sum(weight_wave) OVER (PARTITION BY rwbasis) AS population,
        {% endif %}
        hhkey,
        argMax(rwbasis, dt_start) AS rwbasis
    FROM pet.hh_weights_fullmass
    WHERE (id_panel={{ id_panel }}) AND (dt_start >= '{{ period_start }}') AND (dt_end <= '{{ period_end }}')
    GROUP BY hhkey
) p USING (hhkey)
