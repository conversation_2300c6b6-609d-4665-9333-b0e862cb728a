from clickhouse_driver import Client
import re
from tabulate import tabulate
import logging
import time
import traceback
from typing import Optional, Dict, Any, List, Callable, Tuple
import pandas as pd

from src.core.config import config
from src.core.exceptions import ConnectionError, QueryError
from src.utils.error_handling import log_exception
from src.utils.formatting import format_duration, format_bytes
from src.utils.clickhouse_error_parser import (
    parse_clickhouse_error,
    is_clickhouse_error,
)


class ClickHouseConnection:
    """Class for managing connections to ClickHouse database using clickhouse-driver.

    This class provides methods for executing queries, creating temporary tables,
    and managing resources in ClickHouse using the native clickhouse-driver package.
    """

    def __init__(
        self,
        host: Optional[str] = None,
        port: Optional[int] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        database: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
        session_name: Optional[str] = None,
        send_receive_timeout: int = 7200,  # 2 hours in seconds
        connection_type: str = "cluster",  # "storage" or "cluster"
    ):
        """Initialize a ClickHouse connection.

        Args:
            host: ClickHouse server hostname or IP
            port: ClickHouse server port (default: 9000 for native protocol)
            user: Username for authentication
            password: Password for authentication
            database: Default database to use
            settings: Additional ClickHouse settings
            session_name: Session name for tracking
            send_receive_timeout: Timeout for send/receive operations in seconds
            connection_type: Type of connection ("storage" or "cluster")
        """
        # Use provided values or fall back to config
        self.host = host or config.clickhouse_host
        # clickhouse-driver uses port 9000 by default (native protocol)
        self.port = port or getattr(config, "clickhouse_native_port", 9000)
        self.user = user or config.clickhouse_user
        self.password = password or config.clickhouse_password
        self.database = database or config.clickhouse_database

        self.settings = settings or {}
        self.session_name = session_name
        self.send_receive_timeout = send_receive_timeout
        self.connection_type = connection_type.upper()

        # Initialize logger with connection type to differentiate from clickhouse-connect
        self.logger = logging.getLogger(f"ClickHouseDriverConnection_{connection_type}")

        # Connect to ClickHouse
        self.client = self.connect_with_client()

    def __enter__(self):
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Context manager exit method.

        Args:
            exc_type: Exception type if an exception was raised
            exc_val: Exception value if an exception was raised
            exc_tb: Exception traceback if an exception was raised
        """
        self.close()

    def connect_with_client(self) -> Any:
        """Establishes and returns a ClickHouse client connection.

        Returns:
            ClickHouse client object or None if connection fails

        Raises:
            ConnectionError: If required connection parameters are missing
        """
        # Validate required connection parameters
        if not self.host:
            error_msg = "ClickHouse host is not specified"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        if not self.user:
            self.logger.warning("ClickHouse user is not specified, using default")

        if not self.database:
            self.logger.warning("ClickHouse database is not specified, using default")

        try:
            # Create client connection using clickhouse-driver
            client = Client(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                settings=self.settings,
                send_receive_timeout=self.send_receive_timeout,
            )

            # Test connection by getting server version
            result = client.execute("SELECT version()")
            if (
                isinstance(result, (list, tuple))
                and len(result) > 0
                and len(result[0]) > 0
            ):
                version = result[0][0]
                self.logger.info(
                    f"[{self.connection_type}] [{'=' * 40} Connected to ClickHouse version {version} {'=' * 40}]"
                )
            return client

        except Exception as e:
            error_msg = f"ClickHouse connection failed: {e}"
            self.logger.error(error_msg)
            return None

    def close(self) -> None:
        """Closes the ClickHouse client connection."""
        if self.client:
            self.client.disconnect()
            self.logger.info("ClickHouse connection closed")

    def get_query_result(
        self,
        query: str,
        limit: Optional[int] = None,
        get_info: bool = False,
        logs: bool = True,
        settings: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Executes a query and optionally retrieves performance metrics.

        Args:
            query: SQL query to execute
            limit: Optional row limit to apply
            get_info: Whether to attach performance metrics to result
            logs: Whether to log query execution details
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            QueryResult object containing data and optionally metrics

        Raises:
            ConnectionError: If connection is not established
            QueryError: If query execution fails
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        if not query:
            error_msg = "Query cannot be empty"
            self.logger.error(error_msg)
            raise QueryError(error_msg)

        # Apply limit if specified
        final_query = query + (f" LIMIT {limit}" if limit else "")

        try:
            # Log query execution if enabled
            if logs:
                # Clean query for logging (remove extra whitespace)
                cleaned_query = re.sub(r"\s+", " ", final_query).strip()

                # Truncate long queries in log messages
                log_message = (
                    cleaned_query[:100] + "..."
                    if len(cleaned_query) > 100
                    else cleaned_query
                )
                self.logger.info("Executing query: %s", log_message)

            # Execute the query with custom settings if provided
            start_time = time.time()

            # Merge settings if provided
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            # Execute query using clickhouse-driver
            result_data = self.client.execute(final_query, settings=query_settings)
            execution_time = time.time() - start_time

            # Create a result object that mimics clickhouse-connect's QueryResult
            class QueryResult:
                def __init__(self, data, query_id=None):
                    self.result_rows = data
                    self.result_set = data
                    self.query_id = query_id or f"driver_{int(time.time() * 1000)}"
                    self.summary = {}
                    self.info = ""

            query_result = QueryResult(result_data)

            # Log completion if enabled
            if logs:
                self.logger.info(
                    "Query completed in %s: ID %s",
                    format_duration(execution_time * 1000),
                    query_result.query_id,
                )

            # Attach performance metrics if requested
            if get_info:
                self._attach_query_info(query_result)
                if hasattr(query_result, "info"):
                    self.logger.info("Query info: %s", query_result.info)

            return query_result

        except Exception as e:
            error_msg = f"Query execution failed: {e}"
            self.logger.error(error_msg)
            self.logger.error("Failed query: \n%s", final_query)

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = final_query

            # Raise the enhanced exception without chaining
            raise query_error

    def _attach_query_info(self, query_result: Any) -> None:
        """Attaches performance metrics from query summary to result object.

        Args:
            query_result: ClickHouse query result object
        """
        # Note: clickhouse-driver doesn't provide the same detailed summary as clickhouse-connect
        # We'll create a basic info structure for compatibility
        try:
            info = {
                "query_duration": "N/A (driver mode)",
                "read_rows": "N/A",
                "read_bytes": "N/A",
                "written_rows": "N/A",
                "written_bytes": "N/A",
                "total_rows_to_read": "N/A",
                "result_rows": len(query_result.result_rows)
                if query_result.result_rows
                else 0,
                "result_bytes": "N/A",
                "query_id": query_result.query_id,
            }

            # Try to attach info to result object
            try:
                query_result.info = info
            except AttributeError:
                self.logger.debug("Couldn't attach info to QueryResult object")
                self.logger.info(
                    "Query metrics:\n%s",
                    tabulate([info], headers="keys", tablefmt="grid"),
                )
        except (KeyError, ValueError, TypeError) as e:
            self.logger.warning("Error processing query summary: %s", e)

    def get_query_info(
        self, query_id: str, max_attempts: int = 10, retry_delay: float = 0.5
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieves detailed metrics from system.query_log.
        Use for additional metrics not available in summary.

        Args:
            query_id: The query ID to retrieve metrics for
            max_attempts: Maximum number of retry attempts
            retry_delay: Delay between retry attempts in seconds

        Returns:
            Dictionary with query metrics or None if retrieval fails

        Raises:
            ConnectionError: If connection is not established
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Escape query_id to prevent SQL injection
        safe_query_id = query_id.replace("'", "''")

        query = f"""
        SELECT memory_usage, peak_threads
        FROM system.query_log
        WHERE initial_query_id = '{safe_query_id}' AND type = 'QueryFinish'
        """

        attempt = 0
        while attempt < max_attempts:
            try:
                # Query the system.query_log table
                result = self.client.execute(query)

                # Check if we got any results
                if result:
                    # Extract and format metrics
                    memory_usage = result[0][0]
                    peak_threads = result[0][1]

                    metrics = {
                        "memory_usage": format_bytes(memory_usage),
                        "memory_usage_raw": memory_usage,
                        "peak_threads": peak_threads,
                    }

                    # Log the metrics
                    self.logger.info(
                        "Detailed query metrics for ID %s:\n%s",
                        query_id,
                        tabulate([metrics], headers="keys", tablefmt="grid"),
                    )
                    return metrics

                # If no results found, retry after delay
                self.logger.debug(
                    "No query log data found for ID %s (attempt %d/%d)",
                    query_id,
                    attempt + 1,
                    max_attempts,
                )
                attempt += 1

                # Wait before retrying
                if attempt < max_attempts:
                    time.sleep(retry_delay)

            except Exception as e:
                self.logger.error(
                    "Failed to retrieve query log for ID %s: %s", query_id, e
                )
                return None

        # If we've exhausted all attempts
        self.logger.warning(
            "No query log data found for ID %s after %d attempts",
            query_id,
            max_attempts,
        )
        return None

    def create_table(
        self,
        query: str,
        table_name: str,
        engine: str = "MergeTree()",
        order_by: Optional[str] = None,
        partition_by: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Create a permanent table in ClickHouse.

        Args:
            query: Query to populate the table or table schema
            table_name: Name for the table
            engine: ClickHouse engine to use (default: MergeTree())
            order_by: Required ORDER BY clause for MergeTree tables
            partition_by: Optional PARTITION BY clause
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            Either the name of the created table if successful,
            or a tuple ("ERROR", QueryError) if an error occurred

        Raises:
            ConnectionError: If connection is not established
            ValueError: If table_name or query is empty or if order_by is missing for MergeTree
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not table_name:
            error_msg = "Table name cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not query:
            error_msg = "Query cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # Validate ORDER BY for MergeTree engines
        if "MergeTree" in engine and not order_by:
            error_msg = "ORDER BY clause is required for MergeTree tables"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        start_time = time.time()
        create_query = ""

        try:
            # Create table query
            create_query = f"""
                CREATE TABLE {table_name}
                ENGINE = {engine}
                {f"PARTITION BY {partition_by}" if partition_by else ""}
                {f"ORDER BY {order_by}" if order_by else ""}
                AS {query}
            """

            # Log the operation
            self.logger.info(f"Creating table {table_name}...")

            # Execute the command with custom settings if provided
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            self.client.execute(create_query, settings=query_settings)

            # Log success with duration
            duration = format_duration((time.time() - start_time) * 1000)
            self.logger.info(f"Table {table_name} created successfully in {duration}")
            return table_name

        except Exception as e:
            # Log failure with duration
            duration = format_duration((time.time() - start_time) * 1000)
            error_msg = f"Failed to create table {table_name} after {duration}: {e}"
            self.logger.error(error_msg)
            self.logger.error(f"Failed query: {create_query}")

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = create_query

            return ("ERROR", query_error)

    def create_temp_table(
        self,
        query: str,
        table_name: str,
        engine: str = "Memory",
        order_by: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Create a temporary table in ClickHouse.

        Args:
            query: Query to populate the table
            table_name: Name for the temporary table
            engine: ClickHouse engine to use (default: Memory)
            order_by: Optional ORDER BY clause
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            Either the name of the created table if successful,
            or a tuple ("ERROR", QueryError) if an error occurred

        Raises:
            ConnectionError: If connection is not established
            ValueError: If table_name or query is empty
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not table_name:
            error_msg = "Table name cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not query:
            error_msg = "Query cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        start_time = time.time()
        create_query = ""

        try:
            # Create table query
            create_query = f"""
                CREATE TEMPORARY TABLE {table_name}
                ENGINE = {engine}
                {f"ORDER BY {order_by}" if order_by else ""}
                AS {query}
            """

            # Log the operation
            self.logger.debug(f"Creating temporary table {table_name}...")

            # Execute the command with custom settings if provided
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            self.client.execute(create_query, settings=query_settings)

            # Log success with duration
            duration = format_duration((time.time() - start_time) * 1000)
            self.logger.debug(
                f"Temporary table {table_name} created successfully in {duration}"
            )
            return table_name

        except Exception as e:
            # Log failure with duration
            duration = format_duration((time.time() - start_time) * 1000)
            error_msg = (
                f"Failed to create temp table {table_name} after {duration}: {e}"
            )
            self.logger.error(error_msg)
            self.logger.error(f"Failed query: {create_query}")

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = create_query

            return ("ERROR", query_error)

    def drop_temp_table(self, table_name: str) -> bool:
        """Drops a temporary table.

        Args:
            table_name: Name of the temporary table to drop

        Returns:
            bool: True if successful, False otherwise

        Raises:
            ConnectionError: If connection is not established
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        if not table_name:
            self.logger.debug("No table name provided for drop operation")
            return False

        # Check if table is a cluster table
        cluster_table = False
        if "_distributed" in table_name.lower() or "_replicated" in table_name.lower():
            cluster_table = True

        try:
            # Log the operation
            self.logger.debug("Dropping temporary table %s", table_name)

            # Execute the command
            self.client.execute(
                f"DROP {'TEMPORARY' if not cluster_table else ''} TABLE IF EXISTS {table_name}"
                + (" SYNC" if cluster_table else "")
            )

            # Log success
            self.logger.debug("Temporary table %s dropped successfully", table_name)
            return True

        except Exception as e:
            error_msg = f"Failed to drop temp table {table_name}: {e}"
            self.logger.error(error_msg)

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

                # Create a custom QueryError with additional attributes
                query_error = QueryError(error_msg)
                query_error.error_code = error_code
                query_error.error_type = error_type
                query_error.query = f"DROP TEMPORARY TABLE IF EXISTS {table_name}"

                # Raise the enhanced exception without chaining
                raise query_error
            else:
                # For non-ClickHouse errors, just log and return False
                # This is to avoid breaking cleanup operations
                return False

    def delete_rows_from_table(self, table_name: str, condition: str) -> bool:
        """Deletes rows from a specified table based on a condition.

        Args:
            table_name: Name of the table to delete rows from
            condition: WHERE condition for deletion

        Returns:
            bool: True if successful, False otherwise

        Raises:
            ConnectionError: If connection is not established
            ValueError: If table_name or condition is empty
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not table_name:
            error_msg = "Table name cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not condition:
            error_msg = "Condition cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # Construct the delete query
        delete_query = f"""
            ALTER TABLE {table_name}
            DELETE WHERE {condition}
        """

        try:
            # Log the operation
            self.logger.info(
                "Executing delete query on table %s with condition: %s",
                table_name,
                condition,
            )

            # Execute the command
            start_time = time.time()
            self.client.execute(delete_query)
            duration = format_duration((time.time() - start_time) * 1000)

            # Log success
            self.logger.info(
                "Delete query executed successfully on table %s in %s",
                table_name,
                duration,
            )
            return True

        except Exception as e:
            # Log failure
            self.logger.error("Failed to delete rows from table %s: %s", table_name, e)
            self.logger.error("Failed query: %s", delete_query)
            return False

    def add_rows_to_table(
        self,
        table_name: str,
        data: Optional[List] = None,
        source_query: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add rows to a specified table from either direct data or a query.

        Args:
            table_name: Name of the target table
            data: Direct data to insert (list of tuples or list of dicts)
            source_query: Query that produces data to insert
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            bool: True if successful, False otherwise

        Raises:
            ConnectionError: If connection is not established
            ValueError: If table_name is empty or neither data nor source_query is provided
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not table_name:
            error_msg = "Table name cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not data and not source_query:
            error_msg = "Either data or source_query must be provided"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            start_time = time.time()
            query_settings = self.settings.copy()
            self.logger.info(f"Executing cluster DDL query for intserting data to {table_name}...")
            if settings:
                query_settings.update(settings)

            if source_query:
                # Insert from query
                insert_query = f"""
                    INSERT INTO {table_name}
                    {source_query}
                """
                self.client.execute(insert_query, settings=query_settings)
                duration = format_duration((time.time() - start_time) * 1000)
                self.logger.info(
                    f"Added query results to table {table_name} in {duration}"
                )

            elif data:
                # Insert direct data using clickhouse-driver's insert method
                self.client.execute(
                    f"INSERT INTO {table_name} VALUES", data, settings=query_settings
                )
                duration = format_duration((time.time() - start_time) * 1000)
                self.logger.info(
                    f"Added {len(data)} rows to table {table_name} in {duration}"
                )

            return True

        except Exception as e:
            self.logger.error(f"Failed to add rows to table {table_name}: {e}")
            if source_query:
                self.logger.error(f"Failed query: {source_query}")
            return False

    def add_rows_from_query(
        self,
        target_table: str,
        source_query: str,
        settings: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add rows to target table from a query result.

        Args:
            target_table: Name of the table to insert into
            source_query: Query that produces data to insert
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            bool: True if successful, False otherwise

        Raises:
            ConnectionError: If connection is not established
            ValueError: If target_table or source_query is empty
        """
        # This is a convenience wrapper around add_rows_to_table
        return self.add_rows_to_table(
            table_name=target_table, source_query=source_query, settings=settings
        )

    def current_memory_usage(self, percent: bool = False) -> float:
        """Calculate memory usage for ClickHouse server.

        Args:
            percent: If True, returns memory usage as a percentage of total memory,
                    otherwise returns absolute memory usage in bytes

        Returns:
            float: Memory usage (as percentage or bytes)

        Raises:
            ConnectionError: If connection is not established
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        try:
            # Query to get memory usage as percentage or absolute value
            if percent:
                query = """
                    SELECT
                        (current_memory.value / total_memory.value) * 100 AS memory_usage_percent
                    FROM
                        (SELECT value FROM system.metrics WHERE metric = 'MemoryTracking') AS current_memory,
                        (SELECT value FROM system.asynchronous_metrics WHERE metric = 'OSMemoryTotal') AS total_memory
                    """
            else:
                query = """
                    SELECT
                        value AS memory_usage
                    FROM system.metrics
                    WHERE metric = 'MemoryTracking'
                    """

            # Execute query with logging disabled
            query_result = self.get_query_result(query=query, logs=False)

            # Process result
            if query_result and query_result.result_rows:
                memory_usage = float(query_result.result_rows[0][0])
                self.logger.debug(
                    "Current memory usage: %s%s",
                    round(memory_usage, 2),
                    "%" if percent else " bytes",
                )
                return memory_usage
            else:
                self.logger.warning("No memory usage data returned from query")
                return 0.0

        except Exception as e:
            self.logger.error(f"Error getting memory usage: {e}")
            return 0.0  # Return 0 as a safe default value

    def execute_command(
        self, command: str, logs: bool = True, settings: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Executes a command in ClickHouse.

        Args:
            command: Command to execute
            logs: Whether to log the command execution
            settings: Optional dictionary of ClickHouse settings for this command

        Returns:
            bool: True if successful, False otherwise

        Raises:
            ConnectionError: If connection is not established
            ValueError: If command is empty
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not command:
            error_msg = "Command cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Log the command if enabled
            if logs:
                # Clean command for logging (remove extra whitespace)
                cleaned_command = re.sub(r"\s+", " ", command).strip()

                # Truncate long commands in log messages
                log_message = (
                    cleaned_command[:100] + "..."
                    if len(cleaned_command) > 100
                    else cleaned_command
                )
                self.logger.debug("Executing command: %s", log_message)

            # Execute the command with custom settings if provided
            start_time = time.time()
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            self.client.execute(command, settings=query_settings)
            duration = format_duration((time.time() - start_time) * 1000)

            # Log success if enabled
            if logs:
                self.logger.debug("Command executed successfully in %s", duration)

            return True

        except Exception as e:
            error_msg = f"Command execution failed: {e}"
            self.logger.error(error_msg)
            self.logger.error("Failed command: %s", command)

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = command

            # Raise the enhanced exception without chaining
            raise query_error

    def get_query_dataframe(
        self, query: str, limit: Optional[int] = None, logs: bool = True
    ) -> pd.DataFrame:
        """
        Executes a query and returns the result as a pandas DataFrame.

        Args:
            query: SQL query to execute
            limit: Optional row limit to apply
            logs: Whether to log the query execution

        Returns:
            pandas.DataFrame: Query results as DataFrame

        Raises:
            ConnectionError: If connection is not established
            QueryError: If query execution fails or query is empty
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not query:
            error_msg = "Query cannot be empty"
            self.logger.error(error_msg)
            raise QueryError(error_msg)

        # Apply limit if specified
        final_query = query + (f" LIMIT {limit}" if limit else "")

        try:
            # Log query execution if enabled
            if logs:
                # Clean query for logging (remove extra whitespace)
                cleaned_query = re.sub(r"\s+", " ", final_query).strip()

                # Truncate long queries in log messages
                log_message = (
                    cleaned_query[:100] + "..."
                    if len(cleaned_query) > 100
                    else cleaned_query
                )
                self.logger.debug("Executing query: %s", log_message)

            # Execute the query and get DataFrame
            start_time = time.time()

            # Get column names first
            result_data = self.client.execute(final_query, with_column_types=True)
            columns = [col[0] for col in result_data[1]]  # Column names
            data = result_data[0]  # Actual data

            # Create DataFrame
            df = pd.DataFrame(data, columns=columns)
            duration = format_duration((time.time() - start_time) * 1000)

            # Log completion if enabled
            if logs:
                self.logger.debug(
                    "Query completed in %s, returned DataFrame with shape %s",
                    duration,
                    df.shape,
                )

            return df

        except Exception as e:
            error_msg = f"Query execution failed: {e}"
            self.logger.error(error_msg)
            self.logger.error("Failed query: %s", final_query)

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = final_query

            # Raise the enhanced exception without chaining
            raise query_error

    def get_object_definition(
        self, object_name: str, object_type: str = "table"
    ) -> str:
        """
        Get the CREATE statement for a table or view from ClickHouse.

        Args:
            object_name: Name of the table or view. Can be fully qualified (database.name)
            object_type: Type of object ('table' or 'view'). Defaults to 'table'

        Returns:
            str: CREATE statement for the object

        Raises:
            ConnectionError: If connection is not established
            ValueError: If object_name is empty or object_type is invalid
            QueryError: If query execution fails
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not object_name:
            error_msg = "Object name cannot be empty"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        valid_object_types = ["table", "view", "dictionary", "function"]
        if object_type.lower() not in valid_object_types:
            error_msg = f"Invalid object type: {object_type}. Must be one of {valid_object_types}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Split database and object name if provided
            parts = object_name.split(".")
            if len(parts) == 2:
                database, name = parts
            else:
                database = self.database
                name = object_name

            # Command to get the CREATE statement
            command = f"SHOW CREATE {object_type.upper()} {database}.{name}"

            # Execute the command
            self.logger.info(f"Getting {object_type} definition for {database}.{name}")

            try:
                # This will raise QueryError if it fails
                result = self.get_query_result(command)

                # Process the result
                if result and result.result_rows:
                    definition = result.result_rows[0][0]
                    self.logger.info(
                        f"Successfully retrieved {object_type} definition for {database}.{name}"
                    )
                    return definition

                # If no results, raise a specific error
                error_msg = f"No definition found for {object_type} {database}.{name}"
                self.logger.warning(error_msg)
                raise QueryError(error_msg)

            except QueryError as e:
                # Re-raise with more context
                error_msg = (
                    f"Failed to get {object_type} definition for {database}.{name}: {e}"
                )
                self.logger.error(error_msg)
                # Raise without chaining to avoid nested exceptions
                raise QueryError(error_msg)

        except Exception as e:
            # Catch any other exceptions
            error_msg = f"Unexpected error getting {object_type} definition for {object_name}: {e}"
            self.logger.error(error_msg)
            # Raise without chaining to avoid nested exceptions
            raise QueryError(error_msg)

    def create_replicated_table(
        self,
        table_query: str,
        table_name: str,
        engine: str = "ReplicatedMergeTree()",
        order_by: str = "tuple()",
        template: bool = False,
        partition_by: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Create a replicated table in ClickHouse.

        Args:
            table_query: Query for the table
            table_name: Name for the table
            engine: ClickHouse engine to use (default: ReplicatedMergeTree)
            order_by: Required ORDER BY clause for MergeTree tables
            template: If used template for table creation
            partition_by: Optional PARTITION BY clause
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            Either the name of the created table if successful,
            or a tuple ("ERROR", QueryError) if an error occurred

        Raises:
            ConnectionError: If connection is not established
            ValueError: If required parameters are missing
            QueryError: If query execution fails
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not all([table_name, table_query, order_by]):
            error_msg = "Table name, table_query and order_by are required"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        start_time = time.time()
        create_query = ""

        # Parse replicated table name
        replica_parts = table_name.split(".")
        if len(replica_parts) == 2:
            replica_db, replica_name = replica_parts
        else:
            replica_db = self.database
            replica_name = table_name

        table_name = f"{replica_db}.{replica_name}_replicated"

        try:
            # Create replicated table query
            if template:
                create_query = table_query
            else:
                create_query = f"""
                    CREATE TABLE {table_name}
                    ({table_query})
                    ENGINE = {engine}
                    {f"PARTITION BY {partition_by}" if partition_by else ""}
                    ORDER BY ({order_by})
                    SETTINGS index_granularity = 8192
                """

            # Log the operation
            self.logger.info(f"Creating replicated table {table_name}...")

            # Execute the command with custom settings if provided
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            self.client.execute(create_query, settings=query_settings)

            # Log success with duration
            duration = format_duration((time.time() - start_time) * 1000)
            self.logger.info(
                f"Replicated table {table_name} created successfully in {duration}"
            )
            return table_name

        except Exception as e:
            # Log failure with duration
            duration = format_duration((time.time() - start_time) * 1000)
            error_msg = (
                f"Failed to create replicated table {table_name} after {duration}: {e}"
            )
            self.logger.error(error_msg)
            self.logger.error(f"Failed query: {create_query}")

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.query = create_query

            # Raise the enhanced exception without chaining
            return ("ERROR", query_error)

    def create_distributed_table(
        self,
        source_table: str,
        distributed_table: str,
        cluster_name: str = "default_cluster",
        sharding_key: str = "rand()",
        cluster_database: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Create a distributed table in ClickHouse.

        Args:
            source_table: Name of the source table (can be fully qualified with database)
            distributed_table: Name for the distributed table (can be fully qualified)
            cluster_name: Name of the ClickHouse cluster (default: default_cluster)
            sharding_key: Expression for sharding (default: rand())
            settings: Optional dictionary of ClickHouse settings for this query

        Returns:
            Either the name of the created table if successful,
            or a tuple ("ERROR", QueryError) if an error occurred

        Raises:
            ConnectionError: If connection is not established
            ValueError: If required parameters are missing
            QueryError: If query execution fails
        """
        if not self.client:
            error_msg = "Connection not established"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg)

        # Validate input parameters
        if not all([source_table, distributed_table]):
            error_msg = "Source and distributed table names are required"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        start_time = time.time()
        create_query = ""

        try:
            # Parse source table name
            source_parts = source_table.split(".")
            if len(source_parts) == 2:
                source_db, source_name = source_parts
                if cluster_database and source_db != cluster_database:
                    source_db = cluster_database
            else:
                source_db = self.database if not cluster_database else cluster_database
                source_name = source_table

            # Parse distributed table name
            dist_parts = distributed_table.split(".")
            if len(dist_parts) == 2:
                dist_db, dist_name = dist_parts
            else:
                dist_db = self.database if not cluster_database else cluster_database
                dist_name = distributed_table
            dist_name = dist_name + "_distributed"
            distributed_table = f"{dist_db}.{dist_name}"

            # Create distributed table query
            create_query = f"""
                CREATE TABLE {distributed_table}
                AS {source_db}.{source_name}
                ENGINE = Distributed(
                    '{cluster_name}',
                    '{source_db}',
                    '{source_name}',
                    {sharding_key}
                )
            """

            # Log the operation
            self.logger.info(f"Creating distributed table {dist_db}.{dist_name}...")

            # Execute the command with custom settings if provided
            query_settings = self.settings.copy()
            if settings:
                query_settings.update(settings)

            self.client.execute(create_query, settings=query_settings)

            # Log success with duration
            duration = format_duration((time.time() - start_time) * 1000)
            self.logger.info(
                f"Distributed table {dist_db}.{dist_name} created successfully in {duration}"
            )
            return distributed_table

        except Exception as e:
            # Log failure with duration
            duration = format_duration((time.time() - start_time) * 1000)
            error_msg = f"Failed to create distributed table {distributed_table} after {duration}: {e}"
            self.logger.error(error_msg)
            self.logger.error(f"Failed query: {create_query}")

            # Parse ClickHouse error if applicable
            error_info = parse_clickhouse_error(str(e))
            error_code = error_info["error_code"]
            error_type = error_info["error_type"]

            if error_code:
                self.logger.error(
                    f"ClickHouse error code: {error_code}, type: {error_type}"
                )

            # Create a custom QueryError with additional attributes
            query_error = QueryError(error_msg)
            query_error.error_code = error_code
            query_error.error_type = error_type
            query_error.query = create_query

            return ("ERROR", query_error)
