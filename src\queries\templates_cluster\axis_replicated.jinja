{# Template for creating replicated table for pre-axis data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.axis_{{ table_suffix }}_replicated
(
    hhkey UInt64,
    id_trip String,
    rwbasis String,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number String,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact in ("weight_wave", "fullmass", "rw_compensat")  %}
            {{ fact }} Float64,
        {% endif %}
        {% if fact == "BUF" %}
            population Float64,
        {% endif %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            {{ fact }} Float64,
        {% endif %}
    {% endfor %}
    projectc Float64
)
ENGINE = ReplicatedMergeTree()
ORDER BY (
    hhkey,
    rwbasis,
    id_trip
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                , {{ axis_key }}_position_number
            {% endif %}
        {% endif %}
    {% endfor %}
)
SETTINGS index_granularity = 8192
