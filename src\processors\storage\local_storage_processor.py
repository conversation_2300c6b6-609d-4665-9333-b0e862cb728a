"""
Local storage processor for KPI results.

This module handles storage operations on the local ClickHouse instance,
including table creation, data transfer, and local storage management.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from src.core.clickhouse_connect import ClickHouseConnection
from src.models.axis import Period
from .base_storage_processor import BaseStorageProcessor


class LocalStorageProcessor(BaseStorageProcessor):
    """
    Processor for local ClickHouse storage operations.
    
    Handles:
    - Local table creation and management
    - Data transfer from temporary to permanent tables
    - Local storage validation and cleanup
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        msg_logger_func=None,
        progress_tracker=None,
    ):
        """
        Initialize the local storage processor.

        Args:
            connection: ClickHouse connection instance
            msg_logger_func: Optional function for logging messages to the UI
            progress_tracker: Optional progress tracker for real-time updates
        """
        super().__init__(connection, msg_logger_func, progress_tracker)
        self.logger = logging.getLogger(__name__)

    def create_permanent_table(self, table_name: str, structure: List[Dict[str, str]]) -> bool:
        """
        Create permanent table with given structure.
        
        Args:
            table_name: Name of the table to create
            structure: List of column definitions with name and type
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Build column definitions
            column_defs = []
            dimension_cols = []

            for col in structure:
                col_name = col["name"].replace('"', "").replace("'", "").replace(" ", "_")
                col_type = col["type"]
                column_defs.append(f"`{col_name}` {col_type}")

                # Track dimension columns for ORDER BY
                if col_name.endswith("_position_number"):
                    dimension_cols.append(f"`{col_name}`")

            # Use dimensions for ORDER BY or fallback to tuple()
            order_by = ", ".join(dimension_cols) if dimension_cols else "tuple()"

            # Create table query
            create_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {", ".join(column_defs)}
                )
                ENGINE = MergeTree()
                ORDER BY ({order_by})
            """

            self.logger.info(f"Creating permanent table: {table_name}")
            self.logger.debug(f"Create table query: {create_query}")

            self.connection.execute_command(create_query)
            self.logger.info(f"Successfully created permanent table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create permanent table {table_name}: {str(e)}")
            return False

    def transfer_temp_to_permanent(
        self,
        temp_table_name: str,
        result_id: str,
        period: Period,
        query_prefix: Optional[str] = "query_",
    ) -> Dict[str, Any]:
        """
        Transfer data from temporary table to permanent local storage.

        Args:
            temp_table_name: Name of the temporary table (without database prefix)
            result_id: Combined result ID for the job
            period: Period information
            query_prefix: Prefix for query IDs

        Returns:
            Dictionary with transfer status information
        """
        try:
            self.logger.info(
                f"Transferring temp table {temp_table_name} to local storage for period {period.label}"
            )

            # Check if temporary table exists
            if not self.table_exists(temp_table_name):
                error_msg = f"Temporary table {temp_table_name} does not exist"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Get table structure from temporary table
            table_structure = self.get_table_structure(temp_table_name)
            if not table_structure:
                error_msg = f"Failed to get structure of temporary table {temp_table_name}"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            permanent_table = f"kpi_results.data_{result_id}"

            # Create permanent table if it doesn't exist
            if not self.table_exists(permanent_table):
                if not self.create_permanent_table(permanent_table, table_structure):
                    error_msg = f"Failed to create permanent table {permanent_table}"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Transfer data using INSERT INTO ... SELECT
            rows_transferred, transfer_query_id, error = self.transfer_data(
                temp_table_name, permanent_table, query_prefix
            )

            if error:
                return {"success": False, "error": error, "rows_transferred": 0}

            self.logger.info(
                f"Successfully transferred {rows_transferred} rows from {temp_table_name} to local permanent storage"
            )

            return {
                "success": True,
                "error": None,
                "rows_transferred": rows_transferred,
                "permanent_table": permanent_table,
                "transfer_query_id": transfer_query_id,
                "storage_location": "local",
            }

        except Exception as e:
            error_msg = f"Failed to transfer temp table {temp_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def validate_storage(self, table_name: str) -> Dict[str, Any]:
        """
        Validate local storage table.
        
        Args:
            table_name: Name of the table to validate
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation_result = {
                "table_exists": False,
                "row_count": 0,
                "structure_valid": False,
                "error": None
            }

            # Check if table exists
            if not self.table_exists(table_name):
                validation_result["error"] = f"Table {table_name} does not exist"
                return validation_result

            validation_result["table_exists"] = True

            # Get row count
            validation_result["row_count"] = self.count_table_rows(table_name)

            # Check structure
            structure = self.get_table_structure(table_name)
            validation_result["structure_valid"] = structure is not None and len(structure) > 0

            self.logger.info(f"Local storage validation for {table_name}: {validation_result}")
            return validation_result

        except Exception as e:
            error_msg = f"Failed to validate local storage for {table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {
                "table_exists": False,
                "row_count": 0,
                "structure_valid": False,
                "error": error_msg
            }

    def cleanup_temp_tables(self, table_names: List[str]) -> Dict[str, bool]:
        """
        Clean up temporary tables from local storage.
        
        Args:
            table_names: List of table names to clean up
            
        Returns:
            Dictionary mapping table names to cleanup success status
        """
        cleanup_results = {}
        
        for table_name in table_names:
            try:
                if self.table_exists(table_name):
                    self.connection.drop_temp_table(table_name)
                    self.logger.info(f"Cleaned up temporary table: {table_name}")
                    cleanup_results[table_name] = True
                else:
                    self.logger.debug(f"Temporary table {table_name} does not exist, skipping cleanup")
                    cleanup_results[table_name] = True
                    
            except Exception as e:
                self.logger.error(f"Failed to cleanup temporary table {table_name}: {str(e)}")
                cleanup_results[table_name] = False
                
        return cleanup_results