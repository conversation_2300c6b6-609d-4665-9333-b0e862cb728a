"""
Query service for the application.

This module provides a service for building and executing SQL queries.
It encapsulates the functionality of the ClickHouseQuery and QueryProcessor classes.
"""

from typing import Dict, Optional, Tuple, Callable, Any

from src.core.clickhouse_connect import ClickHouseConnection
from src.core.connection_manager import connection_manager
from src.core.config import config
from src.models.kpi import JobParameters
from src.processors.query_builder_processor import QueryBuilderProcessor
from src.queries.query_builder import ClickHouseQuery
from src.processors.query_processor import QueryProcessor
from src.services.base_service import BaseService
from src.utils.data_processing import generate_result_id


class QueryService(BaseService):
    """
    Service for building and executing SQL queries.

    This service encapsulates the functionality of the ClickHouseQuery and
    QueryProcessor classes and provides a more service-oriented interface.
    """

    def __init__(
        self,
        connection: Optional[ClickHouseConnection] = None,
        msg_logger_func: Optional[Callable] = None,
        cluster_mode: bool = False,
        cluster_connection=None,
    ):
        """
        Initialize the query service.

        Args:
            connection: ClickHouse connection (for storage)
            msg_logger_func: Optional function for logging messages to the UI
            cluster_mode: Whether to enable cluster-based processing
            cluster_connection: Optional cluster connection for distributed processing
        """
        super().__init__(msg_logger_func)

        # Store cluster configuration
        self.cluster_mode = cluster_mode
        self.cluster_connection = cluster_connection

        # Get the connection from the connection manager if not provided
        self.connection = connection or connection_manager.get_clickhouse_connection()

    def create_query_builder(
        self,
        job_parameters: JobParameters,
    ) -> ClickHouseQuery:
        """
        Create a query builder with the validated data from JobParameters model.

        Args:
            job_parameters: JobParameters model containing all validated data

        Returns:
            Configured ClickHouseQuery instance
        """
        self.log_message("Creating query builder with validated data")

        # Log the types of data being passed to the query builder
        self.logger.info(
            f"Filters count: {len(job_parameters.filters)}, Axes count: {len(job_parameters.axes)}"
        )
        if job_parameters.facts_axis:
            self.logger.info(f"Facts data count: {len(job_parameters.facts_axis)}")
        if job_parameters.required_facts:
            self.logger.info(
                f"Required facts count: {len(job_parameters.required_facts)}"
            )

        # Get the first period from the periods list
        if not job_parameters.periods:
            raise ValueError(
                "At least one period is required to create a query builder"
            )

        # Prepare cluster configuration if in cluster mode
        cluster_config = None
        if self.cluster_mode:
            cluster_config = {
                "cluster_name": config.clickhouse_cluster_name,
                "database": config.clickhouse_cluster_database,
                "table_suffix": job_parameters.combined_result_id,
            }

        return ClickHouseQuery(
            period=job_parameters.period,
            product_group=job_parameters.product_group,
            filters=job_parameters.filters,
            axes=job_parameters.axes,
            id_panel=job_parameters.id_panel or 1,
            su_fact=job_parameters.su_fact_data,
            facts_axis=job_parameters.facts_axis,
            required_facts=job_parameters.required_facts,
            cluster_mode=self.cluster_mode,
            cluster_config=cluster_config,
        )

    def create_query_processor(
        self, query_builder: ClickHouseQuery, progress_tracker=None
    ) -> QueryProcessor:
        """
        Create a query processor with the specified query builder.

        Args:
            query_builder: ClickHouseQuery instance
            progress_tracker: Optional progress tracker for real-time updates

        Returns:
            Configured QueryProcessor instance
        """
        self.log_message("Creating query processor")

        return QueryProcessor(
            self.connection,
            query_builder,
            progress_tracker=progress_tracker,
            cluster_connection=self.cluster_connection if self.cluster_mode else None,
        )

    def process_query(
        self,
        query_processor: QueryProcessor,
        job_parameters: JobParameters,
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Process a query and store the results.

        Args:
            query_processor: QueryProcessor instance
            job_parameters: JobParameters model containing all validated data

        Returns:
            Tuple of (Result table name if successful or None, Error info if failed or None)
        """
        self.log_message(f"Processing query for period {job_parameters.period.label}")

        # Create QueryBuilderProcessor instance with the query builder from query_processor
        self.log_message(f"Generating queries for period {job_parameters.period.label}")
        query_builder_processor = QueryBuilderProcessor(query_processor.query_builder)
        table_suffix = f"{job_parameters.combined_result_id}_{
            generate_result_id(
                job_parameters.job_id,
                job_parameters.analysis_name,
                job_parameters.period.label if job_parameters.period else 'Unknown',
                job_parameters.kpi_type,
            )[15:]
        }"

        # Generate queries with ids
        queries, error = query_builder_processor.generate_queries(
            job_parameters.period,
            job_parameters.kpi_type,
            table_suffix
            if self.cluster_mode
            else "_".join(job_parameters.combined_result_id.split("_")[:2]) + "_",
            job_parameters.json_mode,
        )
        if error:
            self.logger.error(f"Failed to generate queries: {error}")
            return None, error

        # Process the query - use cluster mode if enabled
        period_label = (
            job_parameters.period.label if job_parameters.period else "Unknown"
        )
        self.log_message(f"Executing queries for period {period_label}")

        if self.cluster_mode and hasattr(query_processor, "process_cluster_query"):
            self.log_message("Using cluster-based query processing")
            job_parameters.final_result_table, error = query_processor.process_cluster_query(
                job_parameters=job_parameters,
                queries=queries,
            )
        else:
            self.log_message("Using standard query processing")
            job_parameters.final_result_table, error = query_processor.process_query(
                job_parameters=job_parameters, queries=queries
            )
        if error:
            # Handle different error formats
            error_msg = (
                error.get("error", str(error))
                if isinstance(error, dict)
                else str(error)
            )
            self.logger.error(f"Failed to process query: {error_msg}")
            return None, error_msg

        return job_parameters.final_result_table, None
