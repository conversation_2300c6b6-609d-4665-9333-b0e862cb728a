{# Simplified render_axis_join macro without start_pos and end_pos #}
{% macro render_axis_join(axis_key, axis_data) %}
{% if axis_data["type"] == "axsh" %}
    INNER JOIN (
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH cte_table AS (
        SELECT * FROM {{ axis_data["ddl"]["cte"][13:-1] }} AND dt_record BETWEEN '{{ period_start }}' AND toStartOfMonth(toDate('{{ period_end }}'))) ct
        INNER JOIN (
            SELECT hhkey, MAX(dt_start) AS dt_start
            FROM pet.hh_weights_fullmass
            WHERE dt_end BETWEEN '{{ period_start }}' AND '{{ period_end }}'
            GROUP BY hhkey
        ) hh ON ct.hhkey = hh.hhkey AND ct.dt_record = hh.dt_start)
    {% endif %}
    {# Add queries with UNION ALL #}
    {% if axis_data["ddl"]["queries"]|length > 0 %}
        {{ axis_data["ddl"]["queries"][0] }}
        {% for query in axis_data["ddl"]["queries"][1:] %}
            UNION ALL
            {{ query }}
        {% endfor %}
    {% endif %}
    ) {{ axis_key }} USING (hhkey)
{% endif %}
{% endmacro %}


WITH add_axsh AS (
SELECT
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] == "axsh" %}
                position_number AS {{ axis_key }}_position_number,
            {% else %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    rwbasis,
    {% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
        countDistinctIf(hhkey, buyers_ww != 0) AS buyers_raw,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["penetration", "frequency", "buyers_in_store", "spend_per_buyer", "buyers_potential", "closure_rate"])|list %}
        sum(buyers_ww) AS buyers_ww,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["frequency", "packs_per_trip", "trips_in_store", "spend_per_trip", "trips_potential"])|list %}
        sum(trips_fullmass) AS trips,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["trips_potential"])|list %}
        sum(trips_anywhere_fm) AS trips_anywhere,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["buyers_potential"])|list %}
        sum(buyers_anywhere_ww) AS buyers_anywhere_ww,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_in_store"])|list %}
        sum(value_rp) AS value_rp,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["packs_in_store"])|list %}
        sum(number_rp) AS number_rp,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_potential"])|list %}
        sum(value_buyers_anywhere) AS value_buyers_anywhere,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["shopper_loyalty"])|list %}
        sum(value_buyers_elsewhere) AS value_buyers_elsewhere,
    {% endif %}
    {% if "BUF" in required_facts or "population" in required_facts %}
        any(p.population) AS population,
    {% endif %}
    anyIf(projectc, projectc > 0) as projectc
FROM buyers_final a
{% if "BUF" in required_facts or "population" in required_facts or "axsh" in axes.values()|map(attribute="type") or "flt-h" in filters.values()|map(attribute="type") %}
    INNER JOIN (
        WITH population AS (
        SELECT
        hhkey,
        sum(fullmass) / (dateDiff('month', toDate('{{ period_start }}'), toDate('{{ period_end }}')) + 1) AS weight_wave
        FROM pet.hh_weights_fullmass
        WHERE (id_panel={{ id_panel }}) AND (dt_start >= '{{ period_start }}') AND (dt_end <= '{{ period_end }}')
        GROUP BY hhkey
        )
        SELECT
            hhkey,
            sum(weight_wave)
            {% if "axsh" in axes.values()|map(attribute="type") %}
                OVER (PARTITION BY position_number) AS population,
            autolabel,
            position_number
                {% else %}
                OVER ()AS population
            {% endif %}
        FROM population
        {% if "axsh" in axes.values()|map(attribute="type") %}
            {% for axis_key, axis_data in axes.items() %}
                {% if axis_data["type"] is not none %}
                    {{ render_axis_join(axis_key, axis_data) }}
                {% endif %}
            {% endfor %}
        {% endif %}
        {% for filter_key, filter_data in filters.items() %}
            {% if filter_data["type"] == "flt-h" %}
                WHERE hhkey IN
                (SELECT hhkey FROM (
                    {% if filter_data["ddl"]["cte"] %}
                        WITH {{ filter_data["ddl"]["cte"] }}
                    {% endif %}
                    {% if filter_data["ddl"]["queries"]|length > 0 %}
                        {{ filter_data["ddl"]["queries"][0] }}
                        {% for query in filter_data["ddl"]["queries"][1:] %}
                            UNION ALL
                            {{ query }}
                        {% endfor %}
                    {% endif %}
                    ) filter_hh
                    INNER JOIN (
                        SELECT hhkey, MAX(dt_start) AS dt_start
                        FROM pet.hh_weights_fullmass 
                        WHERE dt_end BETWEEN '{{ period_start }}' AND '{{ period_end }}'
                        GROUP BY hhkey
                    ) right_date ON filter_hh.hhkey = right_date.hhkey AND filter_hh.dt_year = right_date.dt_start
                )
            {% endif %}
        {% endfor %}
    )  p USING (hhkey)
    {% elif "axsh" in axes.values()|map(attribute="type") %}
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ render_axis_join(axis_key, axis_data) }}
        {% endif %}
    {% endfor %}
{% endif %}
GROUP BY rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        , {{ axis_key }}_position_number
    {% endif %}
{% endfor %}
),
rwbasis_with_BUFS AS (
SELECT
    a.rwbasis AS rwbasis,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            a.{{ axis_key }}_position_number AS {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    argMin(d.buyers_ww, d.third_axis_position_number) AS buyers_total_ww,
    argMin(d.value, d.third_axis_position_number) AS product_value_total,
    argMin(d.BUF, d.third_axis_position_number) AS BUF2,
    argMin(c.BUF, c.first_axis_position_number) AS BUF1,
    any(b.BUF) AS BUF0
FROM add_axsh a
LEFT JOIN Buyers_Uplift_Factor b ON a.rwbasis = b.rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none and axis_data["type"] != "axsh" %}
        AND a.{{ axis_key }}_position_number = b.{{ axis_key }}_position_number
    {% endif %}
{% endfor %}
LEFT JOIN Buyers_Uplift_Factor c ON a.rwbasis = c.rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none and axis_key == "third_axis" %}
        AND a.{{ axis_key }}_position_number = c.{{ axis_key }}_position_number
    {% endif %}
{% endfor %}
LEFT JOIN Buyers_Uplift_Factor d ON a.rwbasis = d.rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none and axis_data["type"] != "axsh" and axis_key != "third_axis" %}
        AND a.{{ axis_key }}_position_number = d.{{ axis_key }}_position_number
    {% endif %}
{% endfor %}
GROUP BY rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        , {{ axis_key }}_position_number
    {% endif %}
{% endfor %}
),
{# Calculating KPI #}
kpis AS (
SELECT
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    rwbasis,
    {% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
        buyers_raw,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["penetration", "frequency", "buyers_in_store", "spend_per_buyer", "buyers_potential", "closure_rate"])|list %}
        buyers_ww * BUF0 AS buyers_in_shop,
        CASE
            WHEN (buyers_in_shop + (buyers_anywhere_ww - buyers_ww) * BUF1) > buyers_total_ww * BUF2 THEN buyers_total_ww * BUF2
            ELSE (buyers_in_shop + (buyers_anywhere_ww - buyers_ww) * BUF1)
        END AS buyers_potential,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["frequency", "packs_per_trip", "trips_in_store", "spend_per_trip", "trips_potential"])|list %}
        trips,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["trips_potential"])|list %}
        (trips + (trips_anywhere - trips) * BUF1) AS trips_potential,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["buyers_anywhere"])|list %}
        buyers_anywhere_ww,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_in_store", "vpe", "shopper_loyalty"])|list %}
        value_rp AS value_in_shop,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["packs_in_store"])|list %}
        number_rp,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_potential"])|list %}
        CASE
            WHEN (value_rp + (value_buyers_anywhere - value_rp) * BUF1) > product_value_total THEN product_value_total
            ELSE (value_rp + (value_buyers_anywhere - value_rp) * BUF1)
        END AS value_potential,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["shopper_loyalty"])|list %}
        (value_rp + ((value_buyers_elsewhere - value_rp) * BUF0)) AS loyalty_base,
    {% endif %}
    {% if "BUF" in required_facts or "population" in required_facts %}
        population,
    {% endif %}
    projectc
FROM add_axsh a
LEFT JOIN rwbasis_with_BUFS USING (
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {{ axis_key }}_position_number,
    {% endif %}
{% endfor %}
rwbasis)
)
SELECT
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
        sum(buyers_raw) AS buyers_raw,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["penetration", "frequency", "buyers_in_store", "spend_per_buyer", "buyers_potential", "closure_rate"])|list %}
        sum(buyers_in_shop) AS buyers_in_shop,
        sum(buyers_potential) AS buyers_potential,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["frequency", "packs_per_trip", "trips_in_store", "spend_per_trip", "trips_potential"])|list %}
        sum(trips) AS trips_in_store,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["trips_potential"])|list %}
       sum(trips_potential) AS trips_potential,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["buyers_anywhere"])|list %}
        sum(buyers_anywhere_ww) AS buyers_anywhere_ww,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_in_store", "vpe", "shopper_loyalty"])|list %}
        sum(value_in_shop) AS value_in_shop,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["packs_in_store"])|list %}
        any(number_rp) AS number_rp,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["value_potential", "propensity", "vpe"])|list %}
        sum(value_potential) AS value_potential,
        sum(value_potential_total) AS value_potential_total,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "in", ["shopper_loyalty"])|list %}
        sum(loyalty_base) AS loyalty_base,
    {% endif %}
    {% if "BUF" in required_facts or "population" in required_facts %}
        any(population) AS population,
    {% endif %}
    any(projectc) AS projectc
FROM kpis
LEFT JOIN (
    SELECT
        {% for axis_key, axis_data in axes.items() %}
            {% if axis_data["type"] is not none and axis_key != "third_axis" %}
                {{ axis_key }}_position_number,
            {% elif axis_data["type"] is not none and axis_key == "third_axis" %}
                argMin(value_potential, {{ axis_key }}_position_number) AS value_potential_total,
            {% endif %}
        {% endfor %}
        rwbasis
    FROM kpis
    GROUP BY rwbasis
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none and axis_key != "third_axis" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endfor %}
    ) v USING (
    rwbasis
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none and axis_key != "third_axis" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endfor %}
    )
GROUP BY
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if not loop.first %}, {% endif %}
            {{ axis_key }}_position_number
    {% endif %}
{% endfor %}
