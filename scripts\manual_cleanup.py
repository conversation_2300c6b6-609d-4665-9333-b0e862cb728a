#!/usr/bin/env python3
"""
Manual KPI Results Cleanup Script

This script provides manual cleanup operations for KPI result tables and metadata.
It can be used for one-off cleanup tasks, testing, or emergency cleanup operations.

Usage:
    python scripts/manual_cleanup.py --help
    python scripts/manual_cleanup.py --dry-run --expired
    python scripts/manual_cleanup.py --superseded --max-tables 10
    python scripts/manual_cleanup.py --job-id 12345 --force

Author: KPIClick System
Created: 2025-01-01
"""

import argparse
import logging
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.core.clickhouse_connect import ClickHouseConnection
from src.core.config import config
from src.utils.multi_storage_cleanup import MultiStorageCleanupManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cleanup.log')
    ]
)
logger = logging.getLogger(__name__)

class ManualCleanupTool:
    """Manual cleanup tool for KPI results."""
    
    def __init__(self, dry_run: bool = True):
        """
        Initialize the cleanup tool.

        Args:
            dry_run: If True, only simulate operations without making changes
        """
        self.dry_run = dry_run
        self.connection = None
        self.multi_storage_manager = None
        self.stats = {
            'jobs_identified': 0,
            'jobs_updated': 0,
            'tables_dropped': 0,
            'errors': 0,
            'storage_type_breakdown': {'cluster': 0, 'storage': 0}
        }
        
    def connect(self) -> bool:
        """Establish database connection and initialize multi-storage support."""
        try:
            # Establish primary connection
            self.connection = ClickHouseConnection()
            logger.info("Connected to ClickHouse database")

            # Initialize multi-storage manager
            try:
                self.multi_storage_manager = MultiStorageCleanupManager(dry_run=self.dry_run)
                if self.multi_storage_manager.connect():
                    logger.info("Multi-storage cleanup manager initialized successfully")
                else:
                    logger.warning("Failed to initialize multi-storage manager, using legacy cleanup method")
                    self.multi_storage_manager = None
            except Exception as e:
                logger.warning(f"Multi-storage manager initialization failed: {e}, using legacy cleanup method")
                self.multi_storage_manager = None

            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connections."""
        if self.multi_storage_manager:
            self.multi_storage_manager.disconnect()
            logger.info("Multi-storage manager disconnected")

        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    def find_expired_jobs(self, retention_buffer_days: int = 7, stuck_timeout_hours: int = 3, max_results: int = 100) -> List[Dict[str, Any]]:
        """
        Find jobs that have exceeded their retention period or are stuck in progress.

        This includes:
        - Jobs with status='done' that have exceeded retention_days + buffer_days
        - Jobs with status='in_progress' that have been running for more than stuck_timeout_hours

        Args:
            retention_buffer_days: Extra days before considering expired
            stuck_timeout_hours: Hours after which in_progress jobs are considered stuck
            max_results: Maximum number of results to return

        Returns:
            List of expired job records
        """
        query = f"""
        SELECT
            id, job_id, final_result_table, created_at, retention_days,
            lifecycle_status, analysis_name, username, status,
            CASE
                WHEN status = 'done' AND created_at < (now() - toIntervalDay(retention_days + {retention_buffer_days})) THEN 'retention_period_exceeded'
                WHEN status = 'in_progress' AND created_at < (now() - toIntervalHour({stuck_timeout_hours})) THEN 'stuck_in_progress'
                ELSE 'unknown'
            END as expiry_reason
        FROM metadata.results_metadata
        WHERE lifecycle_status = 'ACTIVE'
          AND (
            (status = 'done' AND created_at < (now() - toIntervalDay(retention_days + {retention_buffer_days})))
            OR
            (status = 'in_progress' AND created_at < (now() - toIntervalHour({stuck_timeout_hours})))
          )
          -- AND final_result_table != ''
        ORDER BY created_at
        LIMIT {max_results}
        """

        try:
            result = self.connection.get_query_dataframe(query)
            jobs = result.to_dict('records')

            # Count different types of expired jobs
            retention_expired = sum(1 for job in jobs if job['expiry_reason'] == 'retention_period_exceeded')
            stuck_jobs = sum(1 for job in jobs if job['expiry_reason'] == 'stuck_in_progress')

            logger.info(f"Found {len(jobs)} expired jobs: {retention_expired} retention expired, {stuck_jobs} stuck in progress")
            self.stats['jobs_identified'] += len(jobs)
            return jobs
        except Exception as e:
            logger.error(f"Failed to find expired jobs: {e}")
            self.stats['errors'] += 1
            return []
    
    def find_superseded_jobs(self, max_results: int = 100) -> List[Dict[str, Any]]:
        """
        Find jobs that have been superseded by newer results.

        Args:
            max_results: Maximum number of results to return

        Returns:
            List of superseded job records with superseding job information
        """
        query = f"""
        WITH ranked_jobs AS (
            SELECT
                id, job_id, final_result_table, analysis_name, kpi_type,
                id_panel, username, created_at, lifecycle_status, periods,
                ROW_NUMBER() OVER (
                    PARTITION BY analysis_name, kpi_type, id_panel, username, periods
                    ORDER BY created_at DESC
                ) as rn
            FROM metadata.results_metadata
            WHERE lifecycle_status = 'ACTIVE'
              AND status = 'done'
              AND final_result_table != ''
        ),
        superseded_with_superseding AS (
            SELECT
                s.id as superseded_id,
                s.job_id as superseded_job_id,
                s.final_result_table,
                s.analysis_name,
                s.created_at as superseded_created_at,
                s.lifecycle_status,
                n.id as superseding_id,
                n.job_id as superseding_job_id,
                n.created_at as superseding_created_at
            FROM ranked_jobs s
            INNER JOIN ranked_jobs n ON (
                s.analysis_name = n.analysis_name
                AND s.kpi_type = n.kpi_type
                AND s.id_panel = n.id_panel
                AND s.username = n.username
                AND s.periods = n.periods
                AND n.rn = 1  -- The newest job (superseding)
            )
            WHERE s.rn > 1  -- Older jobs (superseded)
        )
        SELECT
            superseded_id as id,
            superseded_job_id as job_id,
            final_result_table,
            analysis_name,
            superseded_created_at as created_at,
            lifecycle_status,
            superseding_id,
            superseding_job_id,
            superseding_created_at
        FROM superseded_with_superseding
        ORDER BY analysis_name, superseded_created_at
        LIMIT {max_results}
        """

        try:
            result = self.connection.get_query_dataframe(query)
            jobs = result.to_dict('records')
            logger.info(f"Found {len(jobs)} superseded jobs with their superseding job information")
            self.stats['jobs_identified'] += len(jobs)
            return jobs
        except Exception as e:
            logger.error(f"Failed to find superseded jobs: {e}")
            self.stats['errors'] += 1
            return []
    
    def find_job_by_id(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Find a specific job by job_id.
        
        Args:
            job_id: The job ID to search for
            
        Returns:
            Job record if found, None otherwise
        """
        query = f"""
        SELECT
            id, job_id, final_result_table, created_at, retention_days,
            lifecycle_status, analysis_name, username, status
        FROM metadata.results_metadata
        WHERE job_id = '{job_id}'
        """

        try:
            result = self.connection.get_query_dataframe(query)
            if not result.empty:
                job = result.iloc[0].to_dict()
                logger.info(f"Found job {job_id}: {job['analysis_name']} ({job['lifecycle_status']})")
                return job
            else:
                logger.warning(f"Job {job_id} not found")
                return None
        except Exception as e:
            logger.error(f"Failed to find job {job_id}: {e}")
            self.stats['errors'] += 1
            return None
    
    def update_job_status(self, job_id: str, new_status: str, reason: str = '', superseded_by: str = '') -> bool:
        """
        Update the lifecycle status of a job.

        Args:
            job_id: Job ID to update
            new_status: New lifecycle status (ACTIVE, OUTDATED, EXPIRED, DELETED)
            reason: Reason for the status change
            superseded_by: ID of the job that superseded this one (for OUTDATED status)

        Returns:
            True if successful, False otherwise
        """
        if self.dry_run:
            superseded_info = f" (superseded by: {superseded_by})" if superseded_by else ""
            logger.info(f"DRY RUN: Would update job {job_id} to status {new_status}{superseded_info}")
            self.stats['jobs_updated'] += 1
            return True

        # Build update query with optional superseded_by field
        update_fields = [
            f"lifecycle_status = '{new_status}'",
            "marked_for_deletion_at = now()",
            f"cleanup_reason = '{reason}'"
        ]

        if superseded_by:
            update_fields.append(f"superseded_by = '{superseded_by}'")

        query = f"""
        ALTER TABLE metadata.results_metadata
        UPDATE {', '.join(update_fields)}
        WHERE id = '{job_id}'
        """

        try:
            self.connection.execute_command(query)
            superseded_info = f" (superseded by: {superseded_by})" if superseded_by else ""
            logger.info(f"Updated job {job_id} to status {new_status}{superseded_info}")
            self.stats['jobs_updated'] += 1
            return True
        except Exception as e:
            logger.error(f"Failed to update job {job_id}: {e}")
            self.stats['errors'] += 1
            return False
    
    def drop_result_table(self, table_name: str, job_id: str) -> bool:
        """
        Drop a result table and update metadata using multi-storage support.

        Args:
            table_name: Name of the table to drop
            job_id: Associated job ID for metadata update

        Returns:
            True if successful, False otherwise
        """
        if self.dry_run:
            logger.info(f"DRY RUN: Would drop table {table_name}")
            self.stats['tables_dropped'] += 1
            return True

        # Use multi-storage manager if available
        if self.multi_storage_manager:
            try:
                drop_result = self.multi_storage_manager.drop_table(table_name)

                # Update stats based on storage type used
                if drop_result['storage_type']:
                    self.stats['storage_type_breakdown'][drop_result['storage_type']] += 1

                if drop_result['success']:
                    if drop_result['table_existed']:
                        logger.info(f"Dropped {drop_result['storage_type']} table {table_name}")
                        self.stats['tables_dropped'] += 1
                    else:
                        logger.warning(f"Table {table_name} does not exist")

                    # Update metadata
                    update_query = f"""
                    ALTER TABLE metadata.results_metadata
                    UPDATE
                        lifecycle_status = 'DELETED',
                        deleted_at = now()
                    WHERE id = '{job_id}'
                    """
                    self.connection.execute_command(update_query)
                    return True
                else:
                    logger.error(f"Failed to drop table {table_name}: {drop_result.get('error', 'Unknown error')}")
                    self.stats['errors'] += 1
                    return False

            except Exception as e:
                logger.error(f"Multi-storage drop failed for table {table_name}: {e}")
                self.stats['errors'] += 1
                return False

        # Legacy cleanup method
        try:
            # Check if table exists
            check_query = f"EXISTS TABLE {table_name}"
            exists_result = self.connection.get_query_result(check_query)
            exists = exists_result.result_rows[0][0] if exists_result.result_rows else False

            if exists:
                # Drop the table using standard syntax
                drop_query = f"DROP TABLE {table_name}"
                self.connection.execute_command(drop_query)
                logger.info(f"Dropped table {table_name}")

                # Update metadata
                update_query = f"""
                ALTER TABLE metadata.results_metadata
                UPDATE
                    lifecycle_status = 'DELETED',
                    deleted_at = now()
                WHERE id = '{job_id}'
                """
                self.connection.execute_command(update_query)

                self.stats['tables_dropped'] += 1
                return True
            else:
                logger.warning(f"Table {table_name} does not exist")
                # Still update metadata to mark as deleted
                update_query = f"""
                ALTER TABLE metadata.results_metadata
                UPDATE
                    lifecycle_status = 'DELETED',
                    deleted_at = now(),
                    cleanup_reason = cleanup_reason || ' (table_not_found)'
                WHERE id = '{job_id}'
                """
                self.connection.execute_command(update_query)
                return True

        except Exception as e:
            logger.error(f"Failed to drop table {table_name}: {e}")
            self.stats['errors'] += 1
            return False
    
    def cleanup_expired_jobs(self, retention_buffer_days: int = 7, stuck_timeout_hours: int = 3, max_tables: int = 50) -> Dict[str, int]:
        """Clean up expired jobs including stuck in-progress jobs."""
        logger.info("Starting cleanup of expired jobs")

        expired_jobs = self.find_expired_jobs(retention_buffer_days, stuck_timeout_hours, max_tables)
        results = {'updated': 0, 'dropped': 0}

        for job in expired_jobs:
            # Get the cleanup reason from the job record
            cleanup_reason = job.get('expiry_reason', 'retention_period_exceeded')

            # Update status to EXPIRED
            if self.update_job_status(job['id'], 'EXPIRED', cleanup_reason):
                results['updated'] += 1
                logger.info(f"Updated job {job['job_id']} to EXPIRED (reason: {cleanup_reason})")

                # Drop the table
                if job['final_result_table'] and self.drop_result_table(job['final_result_table'], job['id']):
                    results['dropped'] += 1

        logger.info(f"Expired jobs cleanup completed: {results['updated']} updated, {results['dropped']} tables dropped")
        return results
    
    def cleanup_superseded_jobs(self, max_tables: int = 50) -> Dict[str, int]:
        """Clean up superseded jobs with proper superseded_by tracking."""
        logger.info("Starting cleanup of superseded jobs")

        superseded_jobs = self.find_superseded_jobs(max_tables)
        results = {'updated': 0, 'dropped': 0}

        for job in superseded_jobs:
            # Update status to OUTDATED with superseding job ID
            superseding_job_id = job.get('superseding_id', '')
            if self.update_job_status(job['id'], 'OUTDATED', 'superseded_by_newer_result', superseding_job_id):
                results['updated'] += 1
                logger.info(f"Job {job['job_id']} superseded by job {job.get('superseding_job_id', 'unknown')}")

                # Drop the table
                if job['final_result_table'] and self.drop_result_table(job['final_result_table'], job['id']):
                    results['dropped'] += 1

        logger.info(f"Superseded jobs cleanup completed: {results['updated']} updated, {results['dropped']} tables dropped")
        return results
    
    def cleanup_specific_job(self, job_id: str, force: bool = False) -> bool:
        """Clean up a specific job by ID."""
        logger.info(f"Starting cleanup of specific job: {job_id}")
        
        job = self.find_job_by_id(job_id)
        if not job:
            return False
        
        if job['lifecycle_status'] == 'ACTIVE' and not force:
            logger.warning(f"Job {job_id} is ACTIVE. Use --force to cleanup anyway.")
            return False
        
        # Update status to DELETED
        if self.update_job_status(job['id'], 'DELETED', 'manual_cleanup'):
            # Drop the table if it exists
            if job['final_result_table']:
                return self.drop_result_table(job['final_result_table'], job['id'])
            return True
        
        return False
    
    def print_stats(self):
        """Print cleanup statistics."""
        print("\n" + "="*50)
        print("CLEANUP STATISTICS")
        print("="*50)
        print(f"Jobs identified: {self.stats['jobs_identified']}")
        print(f"Jobs updated: {self.stats['jobs_updated']}")
        print(f"Tables dropped: {self.stats['tables_dropped']}")
        print(f"Errors: {self.stats['errors']}")
        print(f"Dry run mode: {self.dry_run}")
        print("="*50)

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Manual KPI Results Cleanup Tool')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Simulate operations without making changes (default: True)')
    parser.add_argument('--execute', action='store_true',
                       help='Actually execute operations (overrides --dry-run)')
    parser.add_argument('--expired', action='store_true',
                       help='Clean up expired jobs')
    parser.add_argument('--superseded', action='store_true',
                       help='Clean up superseded jobs')
    parser.add_argument('--job-id', type=str,
                       help='Clean up specific job by ID')
    parser.add_argument('--force', action='store_true',
                       help='Force cleanup even for ACTIVE jobs')
    parser.add_argument('--max-tables', type=int, default=50,
                       help='Maximum number of tables to process (default: 50)')
    parser.add_argument('--retention-buffer', type=int, default=7,
                       help='Extra days before marking as expired (default: 7)')
    parser.add_argument('--stuck-timeout', type=int, default=3,
                       help='Hours after which in_progress jobs are considered stuck (default: 3)')
    parser.add_argument('--list-only', action='store_true',
                       help='Only list jobs, do not perform cleanup')
    
    args = parser.parse_args()
    
    # Determine dry run mode
    dry_run = args.dry_run and not args.execute
    
    if dry_run:
        print("Running in DRY RUN mode - no changes will be made")
    else:
        print("Running in EXECUTE mode - changes will be made!")
        if not args.force:
            confirm = input("Are you sure you want to proceed? (yes/no): ")
            if confirm.lower() != 'yes':
                print("Operation cancelled")
                return
    
    # Initialize cleanup tool
    tool = ManualCleanupTool(dry_run=dry_run)
    
    if not tool.connect():
        logger.error("Failed to connect to database")
        return 1
    
    try:
        # Execute requested operations
        if args.job_id:
            tool.cleanup_specific_job(args.job_id, args.force)
        elif args.expired:
            if args.list_only:
                jobs = tool.find_expired_jobs(args.retention_buffer, args.stuck_timeout, args.max_tables)
                print(f"\nFound {len(jobs)} expired jobs:")
                for job in jobs:
                    reason = job.get('expiry_reason', 'unknown')
                    print(f"  {job['job_id']}: {job['analysis_name']} (created: {job['created_at']}, reason: {reason})")
            else:
                tool.cleanup_expired_jobs(args.retention_buffer, args.stuck_timeout, args.max_tables)
        elif args.superseded:
            if args.list_only:
                jobs = tool.find_superseded_jobs(args.max_tables)
                print(f"\nFound {len(jobs)} superseded jobs:")
                for job in jobs:
                    print(f"  {job['job_id']}: {job['analysis_name']} (created: {job['created_at']})")
            else:
                tool.cleanup_superseded_jobs(args.max_tables)
        else:
            parser.print_help()
            return 1
        
        # Print statistics
        tool.print_stats()
        
    finally:
        tool.disconnect()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
